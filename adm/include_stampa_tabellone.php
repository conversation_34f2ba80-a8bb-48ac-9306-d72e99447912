<?php
/**
 * Mastercom
 *
 * PHP version 7
 *
 * @category   Amministratore
 * @package    Mastercom
 * @subpackage StampaTabellone
 * <AUTHOR> MasterCom <<EMAIL>>
 * @copyright  2024 MasterTraining s.r.l.
 * @license    http://www.mastertraining.it license
 * @link       http://www.mastertraining.it
 * @filesource
 */

$scala_voti = estrai_parametri_singoli('SCALA_VOTI');
$dati_scala_voti = estrai_scala_voti($scala_voti, 'estesa');

$voto_minimo = $dati_scala_voti['voto_minimo'];
$voto_massimo = $dati_scala_voti['voto_massimo'];
$voto_minimo_suff = $dati_scala_voti['voto_minimo_suff'];
$salto_6 = $dati_scala_voti['salto_6'];
$salto_7 = $dati_scala_voti['salto_7'];
$salto_8 = $dati_scala_voti['salto_8'];
$salto_10 = $dati_scala_voti['salto_10'];
$arrotondamento = $dati_scala_voti['arrotondamento'];
$aggiustamento_media = $dati_scala_voti['aggiustamento_media'];
$aggiustamento_ammissione = $dati_scala_voti['aggiustamento_ammissione'];
////parametri per san luca
//$parametro_debiti = "SI";
//$stampa_annotazioni = "SI";
//$celle_colorate = "SI";

$stampa_pei = "NO";
$dsa = false;

$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);

$commento_al_non_voto = estrai_parametri_singoli("COMMENTO_AL_NON_VOTO");
$anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
$esito_manuale_superiori = estrai_parametri_singoli("ESITO_MANUALE_SUPERIORI");

$dati_classe = estrai_classe((int) $id_classe);

$titolo_stampa = 'TABELLONE VOTI';
if ($param_tabellone_stampa_tutti_voti == 'SOLO_RECUPERI') {
    $titolo_stampa = 'TABELLONE RECUPERI';
}

if (intval($dati_classe["id_sede"]) > 0) {
    $dati_sede = estrai_sede_singola($dati_classe["id_sede"]);
    $intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];
    $indirizzo_scuola = $dati_sede["indirizzo"];
    $citta = $dati_sede["descrizione_comune"];
    $telefono = $dati_sede["telefono"];
    $dirigente_scolastico = $dati_sede["nome_dirigente"];
}

//{{{ <editor-fold defaultstate="collapsed" desc="estrazione dal template dei parametri da utilizzare per la stampa e loro memorizzazione nei 2 casi qualifiche e standard">
// Verifico se la stampa è un export per evitare di sovrascrivere i parametri di stampa
if (!$stampe_export) {
    if ($stato_ipsia_interno == "qualifiche" || $stato_ipsia_interno == "stampa_valori_ammissioni") {
        $tipo_stampa = "TABELLONE_TERZE_QUALIFICHE";
        $stringa_parametri = "";

        foreach ($_POST as $nome => $valore) {
            $pos = strpos($nome, "param_tabellone_qualifiche_");

            if ($pos !== false) {
                $nome_variabile = substr($nome, 27);
                ${$nome_variabile} = $valore;
                $stringa_parametri .= $nome_variabile . "#" . $valore . "@";
            }
        }

        $stringa_parametri = substr($stringa_parametri, 0, -1);
        aggiorna_parametri_stampa($stringa_parametri, $tipo_stampa, (int) $current_user);
    } else {
        switch ($dati_classe["classe"]) {
        case "1":
            $tipo_stampa = "TABELLONE_PRIME";
            break;
        case "2":
            $tipo_stampa = "TABELLONE_SECONDE";
            break;
        case "3":
            $tipo_stampa = "TABELLONE_TERZE";
            break;
        case "4":
            $tipo_stampa = "TABELLONE_QUARTE";
            break;
        case "5":
            $tipo_stampa = "TABELLONE_QUINTE";
            break;
        default:
            $tipo_stampa = "TABELLONE_GENERICO";
            break;
        }
        $stringa_parametri = "";

        foreach ($_POST as $nome => $valore) {
            $pos = strpos($nome, "param_tabellone_");

            if ($pos !== false) {
                $nome_variabile = substr($nome, 16);
                ${$nome_variabile} = $valore;
                $stringa_parametri .= $nome_variabile . "#" . $valore . "@";
            }
        }

        $stringa_parametri = substr($stringa_parametri, 0, -1);
        aggiorna_parametri_stampa($stringa_parametri, $tipo_stampa, (int) $current_user);
    }
}
//}}} </editor-fold>

if ($formato_pagina == "A4") {
    if ($orientamento_pagina == "P") {
        $larghezza_pagina = 210;
        $altezza_pagina = 290;
        $divisione_cella = 5;
    } else {
        $larghezza_pagina = 290;
        $altezza_pagina = 203;
        $divisione_cella = 7;
    }
} else {
    if ($orientamento_pagina == "P") {
        $larghezza_pagina = 290;
        $altezza_pagina = 405;
        $divisione_cella = 8;
    } else {
        $larghezza_pagina = 405;
        $altezza_pagina = 290;
        $divisione_cella = 9;
    }
}

$pdf = new NEXUS_PDF($orientamento_pagina, 'mm', $formato_pagina);
//Data loading
$pdf->AddPage();
$pdf->SetAutoPageBreak(false);
$pdf->SetFont('helvetica', '', 8);

//{{{ <editor-fold defaultstate="collapsed" desc="intestazione principale">
$altezza_ridotta = 0;
$logo = estrai_parametri_singoli('LOGO_NOME', $id_classe, 'classe');

if (file_exists('/var/www-source/mastercom/immagini_scuola/' . $logo)) {
    list($width, $height, $type, $attr) = getimagesize('/var/www-source/mastercom/immagini_scuola/' . $logo);

    $new_height_mm = ($height / (72 / 25.4));
    $new_width_mm = ($width / (72 / 25.4));

    if ($new_width_mm <= ($larghezza_pagina - 20)) {
        $x_logo = (($larghezza_pagina - 20) - $new_width_mm) / 2;
        $x_logo += 10;
        $new_width = $new_width_mm;
        $new_height = $new_height_mm;
        $new_height_rounded = ceil($new_height_mm);
    } else {
        $new_width = 190;
        $new_height = ($new_height_mm / $new_width_mm) * $new_width;
        $new_height_rounded = ceil(($new_height_mm / $new_width_mm) * $new_width);
        $x_logo = (($larghezza_pagina - 20) - $new_width) / 2;
        $x_logo += 10;
    }

    inserisci_intestazione_pdf($pdf, (int) $id_classe);

    $altezza_ridotta += 10;
    $altezza_ridotta += $new_height_rounded;
} else {
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->CellFitScale($larghezza_pagina - 10, 6, $intestazione_stampa, 0, 1, 'C');
    $pdf->SetFont('helvetica', '', 12);
    $pdf->CellFitScale($larghezza_pagina - 10, 6, $indirizzo_scuola . ' - ' . $citta . ' - ' . $telefono, 0, 1, 'C');
    $altezza_ridotta += 12;
}

$testo_excel = $intestazione_stampa . chr(13) . chr(10);
$testo_excel .= $indirizzo_scuola . ' -- ' . $citta . ' -- ' . $telefono . chr(13) . chr(10);

$altezza_ridotta += 5;

$pdf->ln(1);
$altezza_ridotta += 1;

//{{{ <editor-fold defaultstate="collapsed" desc="calcolo altezze per parte stampata in fondo">
if (($stampa_firme != "NO") && ($stampa_firme != "")) {
    //{{{ <editor-fold defaultstate="collapsed">
    $prof_classe = estrai_abbinamenti((int) $id_classe, "classe");

    $arr_prof = [];
    foreach ($prof_classe as $professore_singolo) {
        if ($professore_singolo['tipo_materia'] != 'SOSTEGNO') {
            $arr_prof[] = $professore_singolo;
        }
    }
    $elenco_prof_abbinamenti = $arr_prof;

    $elenco_prof_sostegno = estrai_docenti_sostegno_classe($id_classe);
    $elenco_prof = array_merge($elenco_prof_abbinamenti, $elenco_prof_sostegno);

    $cont_prof = 0;
    $cont_righe = 1;
    foreach ($elenco_prof as $prof) {
        if ((20 + (($cont_prof + 1) * 32)) > $larghezza_pagina) {
            $cont_prof = 0;
            $cont_righe++;
        }
        if (($prof[7] != "CON")) {
            $cont_prof++;
        }
    }
    //}}} </editor-fold>
}
$altezza_ridotta += ($cont_righe * 8) + 10;

if ($stampo_debiti_sui_voti != 'NO' && $stampo_debiti_sui_voti != '' && ( ( intval($dati_classe[2]) != 5 && $dati_classe["tipo_indirizzo"] != 1 ) || ( intval($dati_classe[2]) != 3 && $dati_classe["tipo_indirizzo"] == 1 ) )) {
    //{{{ <editor-fold defaultstate="collapsed" desc="calcolo altezza per stampa legenda">
    if (($cont_tot_comma4a > 0) && ($cont_tot_comma4b > 0)) {
        //$pdf->ln(2);
        //$pdf->SetFont('helvetica','',8);
        //$pdf->Cell(15,4,'Legenda: ',0,0,'L');
        //$pdf->Cell(0,4,'Le materie contrassegnate da * indicano la presenza del debito',0,1,'L');
        //$pdf->Cell(15,4,'',0,0,'L');
        //$pdf->Cell(0,4,'Le materie contrassegnate da ** indicano la presenza del debito con recupero autonomo',0,1,'L');
        $altezza_ridotta += 10;
    } else {
        if ($cont_tot_comma4a > 0) {
            //$pdf->ln(2);
            //$pdf->SetFont('helvetica','',8);
            //$pdf->Cell(15,4,'Legenda: ',0,0,'L');
            //$pdf->Cell(0,4,'Le materie contrassegnate da * indicano la presenza del debito',0,1,'L');
            $altezza_ridotta += 6;
        }

        if ($cont_tot_comma4b > 0) {
            //$pdf->ln(2);
            //$pdf->SetFont('helvetica','',8);
            //$pdf->Cell(15,4,'Legenda: ',0,0,'L');
            //$pdf->Cell(0,4,'Le materie contrassegnate da ** indicano la presenza del debito con recupero autonomo',0,1,'L');
            $altezza_ridotta += 6;
        }
    }
    //}}} </editor-fold>
}

if ($visualizza_data == "SI_D") {
    //{{{ <editor-fold defaultstate="collapsed" desc="calcolo altezza per stampa data e firme preside e coordinatore">
    //$pdf->ln(3);
    //$pdf->SetFont('helvetica','B',8);
    //$pdf->CellFitScale($larghezza_pagina - 100,4, $citta . ' lì ' . date ("d/m/Y",$data_selezionata_finale),0,0,'L');
    $altezza_ridotta += 7;
} elseif ($visualizza_data == "SI_D_DS") {
    //$pdf->ln(3);
    //$pdf->SetFont('helvetica','B',8);
    //$pdf->CellFitScale($larghezza_pagina - 100,4, $citta . ' lì ' . date ("d/m/Y",$data_selezionata_finale),0,0,'L');
    //$pdf->CellFitScale(0,5,'Il Dirigente Scolastico',0,1,'C');
    //$pdf->CellFitScale($larghezza_pagina - 100,4,'',0,0,'L');
    //$pdf->CellFitScale(0,5, $dirigente_scolastico,0,0,'C');
    $altezza_ridotta += 13;
} elseif ($visualizza_data == "SI_D_DS_C") {
    //$pdf->ln(3);
    //$pdf->SetFont('helvetica','B',8);
    //$pdf->CellFitScale($larghezza_pagina/3,4, '',0,0,'L');
    //$pdf->CellFitScale(($larghezza_pagina - 20)/3,4, 'Il Dirigente Scolastico',0,0,'C');
    //$pdf->CellFitScale(($larghezza_pagina - 20)/3,4, 'Il Coordinatore di Classe',0,1,'C');
    //$pdf->CellFitScale($larghezza_pagina/3,2, $citta . ' lì ' . date ("d/m/Y",$data_selezionata_finale),0,0,'L');
    //$pdf->CellFitScale(($larghezza_pagina - 20)/3,2, '_____________________________',0,0,'C');
    //$pdf->CellFitScale(($larghezza_pagina - 20)/3,2, '_____________________________',0,1,'C');
    //$pdf->CellFitScale($larghezza_pagina/3,4, '',0,0,'L');
    //$pdf->CellFitScale(($larghezza_pagina - 20)/3,4, $dirigente_scolastico,0,0,'C');
    //$pdf->CellFitScale(($larghezza_pagina - 20)/3,4, "Prof. " . $coordinatore["cognome"] . " " . $coordinatore["nome"],0,0,'C');
    $altezza_ridotta += 13;
    //}}} </editor-fold>
}

if ($verticale_orizzontale == "VERT") {
    $altezza_ridotta += 25;
}
//}}} </editor-fold>
//}}} </editor-fold>

if ($stato_ipsia_interno == "qualifiche") {
    //{{{ <editor-fold defaultstate="collapsed" desc="stampa in caso sia ipsia e tabelloni di qualifica o ammissione">
    $multi_classe_pagelle = estrai_parametri_singoli('MULTI_CLASSE_PAGELLE', $id_classe, 'classe');

    if ($param_tabellone_qualifiche_tipo_periodo == 'ammissione_completa') {
        //{{{ <editor-fold defaultstate="collapsed" desc="calcolo dei valori da inserire nella stampa">
        $parametri_professionali = estrai_parametri_professionali();
        $arrotondamento_parallelo = estrai_parametri_singoli('ARROTONDAMENTO_PARALLELO');
        $arrotondamento_materia = estrai_parametri_singoli('ARROTONDAMENTO_MATERIA', $id_classe, 'classe');
        $arrotondamento_ammissione_calcolato = estrai_parametri_singoli('ARROTONDAMENTO_AMMISSIONE_CALCOLATO');
        $voto_pagellina_totale = [];
        $dati_colonne_ammissione = [];

        //{{{ <editor-fold defaultstate="collapsed" desc="primo quadrimestre">
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_rivisto((int) $id_classe, "7", $elenco_studenti_classe, (int) $current_user);
        $tipo_visualizzazione_voti_primo_quad = identifica_periodo_tipo_voto("7", (int) $id_classe);

        foreach ($elenco_voti_pagelline as $indice => $studente) {
            $cont_voti = 0;
            $totale_voti = 0;

            foreach ($studente as $voto_pagellina) {
                if ($voto_pagellina["id_materia"] != "crediti" && $voto_pagellina["id_materia"] != "ammissione" && $voto_pagellina["id_materia"] != "qualifica" && $voto_pagellina["in_media_pagelle"] == "SI"
                ) {
                    $voto_pagellina_totale[7][$studente[0]['id_studente']][$voto_pagellina["id_materia"]] = $voto_pagellina;
                    $voto_parallelo = 0;
                    $cont_parallelo = 0;

                    if ($tipo_visualizzazione_voti_primo_quad != "voto_singolo" || ($tipo_visualizzazione_voti_primo_quad == "personalizzato" && $voto_pagellina["tipo_voto_personalizzato"] != '1')
                    ) {
                        if ($voto_pagellina[11] != "" && intval($voto_pagellina[11]) != 0) {
                            $totale_voti = $totale_voti + $voto_pagellina[11];
                            $cont_voti++;
                            $voto_parallelo += $voto_pagellina[11];
                            $cont_parallelo++;
                        }

                        if ($voto_pagellina[12] != "" && intval($voto_pagellina[12]) != 0) {
                            $totale_voti = $totale_voti + $voto_pagellina[12];
                            $cont_voti++;
                            $voto_parallelo += $voto_pagellina[12];
                            $cont_parallelo++;
                        }

                        if ($voto_pagellina[13] != "" && intval($voto_pagellina[13]) != 0) {
                            $totale_voti = $totale_voti + $voto_pagellina[13];
                            $cont_voti++;
                            $voto_parallelo += $voto_pagellina[13];
                            $cont_parallelo++;
                        }
                    } else {
                        if ($voto_pagellina[8] != "" && intval($voto_pagellina[8]) != 0) {
                            $totale_voti = $totale_voti + $voto_pagellina[8];
                            $cont_voti++;
                            $voto_parallelo = $voto_pagellina[8];
                            $cont_parallelo = 1;
                        }
                    }

                    if ($cont_parallelo > 0) {
                        $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                    } else {
                        $media_parallelo = 0;
                    }

                    $voto_pagellina_totale[7][$studente[0]['id_studente']][$voto_pagellina["id_materia"]]['media_parallelo'] = $media_parallelo;
                }
            }
            if ($cont_voti > 0) {
                $media = round(($totale_voti / $cont_voti), 1);
            } else {
                $media = 0;
            }

            $valore_ammissione = $media * 10;
            $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_scrutinio_primo_quadrimestre"] = $valore_ammissione;
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="secondo trimestre">
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_rivisto((int) $id_classe, "8", $elenco_studenti_classe, (int) $current_user);
        $tipo_visualizzazione_voti_secondo_trim = identifica_periodo_tipo_voto("8", (int) $id_classe);

        if (is_array($elenco_voti_pagelline)) {
            foreach ($elenco_voti_pagelline as $indice => $studente) {
                $cont_voti = 0;
                $totale_voti = 0;

                foreach ($studente as $voto_pagellina) {
                    if ($voto_pagellina["id_materia"] != "crediti" && $voto_pagellina["id_materia"] != "ammissione" && $voto_pagellina["id_materia"] != "qualifica" && $voto_pagellina["in_media_pagelle"] == "SI"
                    ) {
                        $voto_pagellina_totale[8][$studente[0]['id_studente']][$voto_pagellina["id_materia"]] = $voto_pagellina;
                        $voto_parallelo = 0;
                        $cont_parallelo = 0;

                        if ($tipo_visualizzazione_voti_secondo_trim != "voto_singolo" || ($tipo_visualizzazione_voti_secondo_trim == "personalizzato" && $voto_pagellina["tipo_voto_personalizzato"] != '1')
                        ) {
                            if ($voto_pagellina[11] != "" && intval($voto_pagellina[11]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[11];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[11];
                                $cont_parallelo++;
                            }

                            if ($voto_pagellina[12] != "" && intval($voto_pagellina[12]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[12];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[12];
                                $cont_parallelo++;
                            }

                            if ($voto_pagellina[13] != "" && intval($voto_pagellina[13]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[13];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[13];
                                $cont_parallelo++;
                            }
                        } else {
                            if ($voto_pagellina[8] != "" && intval($voto_pagellina[8]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[8];
                                $cont_voti++;
                                $voto_parallelo = $voto_pagellina[8];
                                $cont_parallelo = 1;
                            }
                        }

                        if ($cont_parallelo > 0) {
                            $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                        } else {
                            $media_parallelo = 0;
                        }

                        $voto_pagellina_totale[8][$studente[0]['id_studente']][$voto_pagellina["id_materia"]]['media_parallelo'] = $media_parallelo;
                    }
                }

                if ($cont_voti > 0) {
                    $media = round(($totale_voti / $cont_voti), 1);
                } else {
                    $media = 0;
                }

                $valore_ammissione = $media * 10;
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_scrutinio_secondo_trimestre"] = $valore_ammissione;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="prove strutturate">
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_rivisto((int) $id_classe, "10", $elenco_studenti_classe, (int) $current_user);
        $tipo_visualizzazione_voti_prove_strutt = identifica_periodo_tipo_voto("10", (int) $id_classe);

        if (is_array($elenco_voti_pagelline)) {
            foreach ($elenco_voti_pagelline as $indice => $studente) {
                $cont_voti = 0;
                $totale_voti = 0;

                foreach ($studente as $voto_pagellina) {
                    if ($voto_pagellina["id_materia"] != "crediti" && $voto_pagellina["id_materia"] != "ammissione" && $voto_pagellina["id_materia"] != "qualifica" && $voto_pagellina["in_media_pagelle"] == "SI"
                    ) {
                        $voto_pagellina_totale[10][$studente[0]['id_studente']][$voto_pagellina["id_materia"]] = $voto_pagellina;
                        $voto_parallelo = 0;
                        $cont_parallelo = 0;

                        if ($tipo_visualizzazione_voti_prove_strutt != "voto_singolo" || ($tipo_visualizzazione_voti_prove_strutt == "personalizzato" && $voto_pagellina["tipo_voto_personalizzato"] != '1')
                        ) {
                            if ($voto_pagellina[11] != "" && intval($voto_pagellina[11]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[11];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[11];
                                $cont_parallelo++;
                            }

                            if ($voto_pagellina[12] != "" && intval($voto_pagellina[12]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[12];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[12];
                                $cont_parallelo++;
                            }

                            if ($voto_pagellina[13] != "" && intval($voto_pagellina[13]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[13];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[13];
                                $cont_parallelo++;
                            }
                        } else {
                            if ($voto_pagellina[8] != "" && intval($voto_pagellina[8]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[8];
                                $cont_voti++;
                                $voto_parallelo = $voto_pagellina[8];
                                $cont_parallelo = 1;
                            }
                        }

                        if ($cont_parallelo > 0) {
                            $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                        } else {
                            $media_parallelo = 0;
                        }

                        $voto_pagellina_totale[10][$studente[0]['id_studente']][$voto_pagellina["id_materia"]]['media_parallelo'] = $media_parallelo;
                    }
                }

                if ($cont_voti > 0) {
                    $media = round(($totale_voti / $cont_voti), 1);
                } else {
                    $media = 0;
                }

                $valore_ammissione = $media * 10;
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_prove_strutturate"] = $valore_ammissione;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="fine anno">
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_rivisto((int) $id_classe, '9', $elenco_studenti_classe, (int) $current_user);
        $tipo_visualizzazione_voti = identifica_periodo_tipo_voto("9", (int) $id_classe);

        if (is_array($elenco_voti_pagelline)) {
            foreach ($elenco_voti_pagelline as $indice => $studente) {
                $totale_voti = 0;
                $cont_voti = 0;

                foreach ($studente as $voto_pagellina) {
                    if ($voto_pagellina["id_materia"] != "crediti" && $voto_pagellina["id_materia"] != "ammissione" && $voto_pagellina["id_materia"] != "qualifica" && $voto_pagellina["in_media_pagelle"] == "SI"
                    ) {
                        $voto_pagellina_totale[9][$studente[0]['id_studente']][$voto_pagellina["id_materia"]] = $voto_pagellina;
                        $voto_parallelo = 0;
                        $cont_parallelo = 0;

                        if ($tipo_visualizzazione_voti != "voto_singolo" || ($tipo_visualizzazione_voti == "personalizzato" && $voto_pagellina["tipo_voto_personalizzato"] != '1')
                        ) {
                            if ($voto_pagellina[11] != "" && intval($voto_pagellina[11]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[11];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[11];
                                $cont_parallelo++;
                            }

                            if ($voto_pagellina[12] != "" && intval($voto_pagellina[12]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[12];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[12];
                                $cont_parallelo++;
                            }

                            if ($voto_pagellina[13] != "" && intval($voto_pagellina[13]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[13];
                                $cont_voti++;
                                $voto_parallelo += $voto_pagellina[13];
                                $cont_parallelo++;
                            }
                        } else {
                            if ($voto_pagellina[8] != "" && intval($voto_pagellina[8]) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina[8];
                                $cont_voti++;
                                $voto_parallelo = $voto_pagellina[8];
                                $cont_parallelo = 1;
                            }
                        }

                        if ($cont_parallelo > 0) {
                            $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                        } else {
                            $media_parallelo = 0;
                        }

                        $voto_pagellina_totale[9][$studente[0]['id_studente']][$voto_pagellina["id_materia"]]['media_parallelo'] = $media_parallelo;
                    }
                }

                if ($cont_voti > 0) {
                    $media = round(($totale_voti / $cont_voti), 1);
                } else {
                    $media = 0;
                }

                $valore_ammissione = $media * $aggiustamento_ammissione;
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_scrutinio_fine_anno"] = $valore_ammissione;
                $percentuale_primo_periodo = estrai_parametri_singoli("PERCENTUALE_MEDIA_PRIMO_PERIODO");
                $percentuale_secondo_periodo = estrai_parametri_singoli("PERCENTUALE_MEDIA_SECONDO_PERIODO");
                $percentuale_fine_anno = estrai_parametri_singoli("PERCENTUALE_MEDIA_FINE_ANNO");
                $percentuale_prove_strutt = estrai_parametri_singoli("PERCENTUALE_MEDIA_PROVE_STRUTTURATE");

                $media_voti_pesata = round(
                    (
                        (
                        (
                        $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_scrutinio_primo_quadrimestre"] *
                        $percentuale_primo_periodo
                        ) +
                        (
                        $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_scrutinio_secondo_trimestre"] *
                        $percentuale_secondo_periodo
                        ) +
                        (
                        $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_scrutinio_fine_anno"] *
                        $percentuale_fine_anno
                        ) +
                        (
                        $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_prove_strutturate"] *
                        $percentuale_prove_strutt
                        )
                        ) /
                        100
                        ), 0
                );

                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["media_voti_pesata"] = $media_voti_pesata;
                $voto_ammissione_calcolato = $media_voti_pesata +
                        $elenco_voti_pagelline[$indice][0]["curriculum_prima"] +
                        $elenco_voti_pagelline[$indice][0]["curriculum_seconda"] +
                        $elenco_voti_pagelline[$indice][0]["stage_professionali"];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["voto_ammissione_calcolato"] = $voto_ammissione_calcolato;
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["curriculum_prima"] = $elenco_voti_pagelline[$indice][0]["curriculum_prima"];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["curriculum_seconda"] = $elenco_voti_pagelline[$indice][0]["curriculum_seconda"];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["stage_professionali"] = $elenco_voti_pagelline[$indice][0]["stage_professionali"];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["id_studente"] = $elenco_voti_pagelline[$indice][0][0];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["registro"] = $elenco_voti_pagelline[$indice][0][1];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["cognome"] = $elenco_voti_pagelline[$indice][0][3];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["nome"] = $elenco_voti_pagelline[$indice][0][2];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["ammesso_esame_qualifica"] = $elenco_voti_pagelline[$indice][0]["ammesso_esame_qualifica"];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["voto_ammissione"] = $elenco_voti_pagelline[$indice][0]["voto_ammissione"];
                $elenco_studenti[$elenco_voti_pagelline[$indice][0][0]]["giudizio_ammissione_terza"] = $elenco_voti_pagelline[$indice][0]["giudizio_ammissione_terza"];

                $sezione_abilitata = "ammissione";
                $template->assign("sezione_abilitata", $sezione_abilitata);

                $elenco_voti_ammissione = [];

                for ($cont_voti_temp = 0; $cont_voti_temp < 101; $cont_voti_temp++) {
                    $elenco_voti_ammissione[] = [
                        'nome'   => $cont_voti_temp,
                        'valore' => $cont_voti_temp
                    ];
                }

                $template->assign("elenco_voti_ammissione", $elenco_voti_ammissione);
                $periodi_scolastici = estrai_parametri_singoli("PERIODI_SCOLASTICI", $id_classe, 'classe');
                $template->assign("periodi_scolastici", $periodi_scolastici);
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="sezione di elaborazione colonne gruppi per ammissione">
        $tot_valori_max = count($voto_pagellina_totale[7]);
        $indice_attuale = 7;

        if ($tot_valori_max < count($voto_pagellina_totale[8])) {
            $indice_attuale = 8;
            $tot_valori_max = count($voto_pagellina_totale[8]);
        }

        if ($tot_valori_max < count($voto_pagellina_totale[9])) {
            $indice_attuale = 9;
            $tot_valori_max = count($voto_pagellina_totale[9]);
        }

        if ($tot_valori_max < count($voto_pagellina_totale[10])) {
            $indice_attuale = 10;
            $tot_valori_max = count($voto_pagellina_totale[10]);
        }

        $cont_gruppi_media = 1;

        foreach ($parametri_professionali['MEDIA'] as $gruppo) {
            //{{{ <editor-fold defaultstate="collapsed" desc="ciclo per gruppi media">
            $cont_stato_gruppo = 0;

            if ($parametri_professionali['gruppi_media'] >= $cont_gruppi_media) {
                $intestazione[$gruppo['nome_gruppo']] = 'Gruppo Media ' . $gruppo['nome_gruppo'];

                if ($gruppo['periodo7_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>I Quadr.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['periodo8_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>II Trim.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['periodo9_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Fine Anno.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['periodo10_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Prove Strutt.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['curriculum1_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum I';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['curriculum2_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum II';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['stage_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Stage';
                    $cont_stato_gruppo++;
                }

                if ($cont_stato_gruppo >= 1) {
                    foreach ($voto_pagellina_totale[9] as $key_id_studente => $studente) {
                        $tot_materie = count($studente);

                        if ($tot_materie == 0) {
                            $tot_materie = 1;
                        }

                        foreach ($studente as $key_id_materia => $valore) {
                            $valore7 = round((($voto_pagellina_totale[7][$key_id_studente][$key_id_materia]['media_parallelo'] * $gruppo['periodo7_perc1']) / $gruppo['periodo7_perc2']), $arrotondamento_parallelo);
                            $valore8 = round((($voto_pagellina_totale[8][$key_id_studente][$key_id_materia]['media_parallelo'] * $gruppo['periodo8_perc1']) / $gruppo['periodo8_perc2']), $arrotondamento_parallelo);
                            $valore9 = round((($voto_pagellina_totale[9][$key_id_studente][$key_id_materia]['media_parallelo'] * $gruppo['periodo9_perc1']) / $gruppo['periodo9_perc2']), $arrotondamento_parallelo);
                            $valore10 = round((($voto_pagellina_totale[10][$key_id_studente][$key_id_materia]['media_parallelo'] * $gruppo['periodo10_perc1']) / $gruppo['periodo10_perc2']), $arrotondamento_parallelo);

                            //calcolo della media delle singole materie
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] = 0;

                            if ($gruppo['periodo7_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore7;
                            }

                            if ($gruppo['periodo8_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore8;
                            }

                            if ($gruppo['periodo9_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore9;
                            }

                            if ($gruppo['periodo10_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore10;
                            }

                            $valore_curriculum1 = round(((($elenco_studenti[$key_id_studente]["curriculum_prima"] * $gruppo['curriculum1_perc1']) / $gruppo['curriculum1_perc2']) / $tot_materie), $arrotondamento_parallelo);
                            $valore_curriculum2 = round(((($elenco_studenti[$key_id_studente]["curriculum_seconda"] * $gruppo['curriculum2_perc1']) / $gruppo['curriculum2_perc2']) / $tot_materie), $arrotondamento_parallelo);
                            $valore_stage = round(((($elenco_studenti[$key_id_studente]["stage_professionali"] * $gruppo['stage_perc1']) / $gruppo['stage_perc2']) / $tot_materie), $arrotondamento_parallelo);

                            if ($gruppo['curriculum1_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore_curriculum1;
                            }

                            if ($gruppo['curriculum2_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore_curriculum2;
                            }

                            if ($gruppo['stage_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore_stage;
                            }
                        }
                    }
                } else {
                    foreach ($voto_pagellina_totale[9] as $key_id_studente => $studente) {
                        foreach ($studente as $key_id_materia => $studente) {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] = '0';
                        }
                    }
                }

                //calcolo della media totale
                foreach ($dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']] as $key_id_studente => $studente) {
                    $conteggio_materie = 0;
                    $conteggio_finale_media = 0;

                    foreach ($studente as $key_mat => $materia) {
                        if ($materia['media'] > 0) {
                            $finale[$key_id_studente][$key_mat]['contributo'] = round($materia['media'], $arrotondamento_materia);
                            $conteggio_finale_media += round($materia['media'], $arrotondamento_materia);
                            $conteggio_materie++;
                        }
                    }

                    if ($conteggio_materie > 0) {
                        $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['intestazione'] = $intestazione[$gruppo['nome_gruppo']];
                        $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['valori'][$key_id_studente]['media'] = round(($conteggio_finale_media / $conteggio_materie), $arrotondamento_parallelo);
                    } else {
                        $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['intestazione'] = $intestazione[$gruppo['nome_gruppo']];
                        $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['valori'][$key_id_studente]['media'] = 0;
                    }
                }
            }

            $cont_gruppi_media++;
            //}}} </editor-fold>
        }

        $cont_gruppi_somma = 1;

        foreach ($parametri_professionali['SOMMA'] as $gruppo) {
            //{{{ <editor-fold defaultstate="collapsed" desc="ciclo per gruppi somma">
            $cont_stato_gruppo = 0;

            if ($parametri_professionali['gruppi_somma'] >= $cont_gruppi_somma) {
                $intestazione[$gruppo['nome_gruppo']] = 'Gruppo Somma ' . $gruppo['nome_gruppo'];

                if ($gruppo['periodo7_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>I Quadr.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['periodo8_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>II Trim.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['periodo9_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Fine Anno.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['periodo10_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Prove Strutt.';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['curriculum1_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum I';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['curriculum2_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum II';
                    $cont_stato_gruppo++;
                }

                if ($gruppo['stage_stato'] == 'SI') {
                    $intestazione[$gruppo['nome_gruppo']] .= '<br>Stage';
                    $cont_stato_gruppo++;
                }

                if ($cont_stato_gruppo >= 1) {
                    foreach ($voto_pagellina_totale[9] as $key_id_studente => $studente) {
                        foreach ($studente as $key_id_materia => $valore) {
                            $valore7 = $voto_pagellina_totale[7][$key_id_studente][$key_id_materia]['somma_parallelo'];
                            $valore8 = $voto_pagellina_totale[8][$key_id_studente][$key_id_materia]['somma_parallelo'];
                            $valore9 = $voto_pagellina_totale[9][$key_id_studente][$key_id_materia]['somma_parallelo'];
                            $valore10 = $voto_pagellina_totale[10][$key_id_studente][$key_id_materia]['somma_parallelo'];
                            //calcolo della somma totale
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] = 0;

                            if ($gruppo['periodo7_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore7;
                            }

                            if ($gruppo['periodo8_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore8;
                            }

                            if ($gruppo['periodo9_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore9;
                            }

                            if ($gruppo['periodo10_stato'] == 'SI') {
                                $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore10;
                            }
                        }

                        $valore_curriculum1 = $elenco_studenti[$key_id_studente]["curriculum_prima"];
                        $valore_curriculum2 = $elenco_studenti[$key_id_studente]["curriculum_seconda"];
                        $valore_stage = $elenco_studenti[$key_id_studente]["stage_professionali"];

                        if ($gruppo['curriculum1_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente]['curriculum1']['somma'] += $valore_curriculum1;
                        }

                        if ($gruppo['curriculum2_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente]['curriculum2']['somma'] += $valore_curriculum2;
                        }

                        if ($gruppo['stage_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente]['stage']['somma'] += $valore_stage;
                        }
                    }
                } else {
                    foreach ($voto_pagellina_totale[9] as $key_id_studente => $studente) {
                        foreach ($studente as $key_id_materia => $studente) {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] = '0';
                        }
                    }
                }

                //calcolo della somma totale
                foreach ($dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']] as $key_id_studente => $studente) {
                    $conteggio_finale_somma = 0;

                    foreach ($studente as $materia) {
                        if ($materia['somma'] > 0) {
                            $conteggio_finale_somma += $materia['somma'];
                        }
                    }

                    $dati_colonne_ammissione['risultati']['gruppi_somma'][$gruppo['nome_gruppo']]['intestazione'] = $intestazione[$gruppo['nome_gruppo']];
                    $dati_colonne_ammissione['risultati']['gruppi_somma'][$gruppo['nome_gruppo']]['valori'][$key_id_studente]['somma'] = $conteggio_finale_somma;
                }
            }

            $cont_gruppi_somma++;
            //}}} </editor-fold>
        }

        //elaborazioni finali di MEDIA e SOMMA
        $valori_finali_ammissioni_studenti = [];

        $cont_gruppi_media = 1;

        foreach ($parametri_professionali['MEDIA'] as $gruppo) {
            $cont_stato_gruppo = 0;

            if ($parametri_professionali['gruppi_media'] >= $cont_gruppi_media) {
                foreach ($dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['valori'] as $key_id_studente => $studente) {
                    $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_media'][$gruppo['nome_gruppo']]['valore'] = $studente['media'];
                    $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_media'][$gruppo['nome_gruppo']]['perc1'] = $gruppo['gruppo_perc1'];
                    $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_media'][$gruppo['nome_gruppo']]['perc2'] = $gruppo['gruppo_perc2'];
                }
            }

            $cont_gruppi_media++;
        }

        $cont_gruppi_somma = 1;

        foreach ($parametri_professionali['SOMMA'] as $gruppo) {
            $cont_stato_gruppo = 0;

            if ($parametri_professionali['gruppi_somma'] >= $cont_gruppi_somma) {
                foreach ($dati_colonne_ammissione['risultati']['gruppi_somma'][$gruppo['nome_gruppo']]['valori'] as $key_id_studente => $studente) {
                    $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_somma'][$gruppo['nome_gruppo']]['valore'] = $studente['somma'];
                    $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_somma'][$gruppo['nome_gruppo']]['perc1'] = $gruppo['gruppo_perc1'];
                    $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_somma'][$gruppo['nome_gruppo']]['perc2'] = $gruppo['gruppo_perc2'];
                }
            }
            $cont_gruppi_somma++;
        }

        foreach ($valori_finali_ammissioni_studenti as $key_id_studente => $studente) {
            $valore_ammissione_da_gruppi = 0;

            foreach ($studente['gruppi_media'] as $valore_gruppo) {
                $valore_ammissione_da_gruppi += (($valore_gruppo['valore'] * $valore_gruppo['perc1']) / $valore_gruppo['perc2']);
            }

            foreach ($studente['gruppi_somma'] as $valore_gruppo) {
                $valore_ammissione_da_gruppi += (($valore_gruppo['valore'] * $valore_gruppo['perc1']) / $valore_gruppo['perc2']);
            }
            $elenco_studenti[$key_id_studente]["voto_ammissione_calcolato"] = round($valore_ammissione_da_gruppi, $arrotondamento_ammissione_calcolato);

            if ($elenco_studenti[$key_id_studente]["ammesso_esame_qualifica"] == "--") {
                if ($elenco_studenti[$key_id_studente]["voto_ammissione_calcolato"] >= 60) {
                    $elenco_studenti[$key_id_studente]["ammesso_esame_qualifica"] = "SI";
                } else {
                    $elenco_studenti[$key_id_studente]["ammesso_esame_qualifica"] = "NO";
                }
            }
        }

        $template->assign("dati_colonne_ammissione_media", $dati_colonne_ammissione['risultati']['gruppi_media']);
        $template->assign("dati_colonne_ammissione_somma", $dati_colonne_ammissione['risultati']['gruppi_somma']);
        $template->assign("elenco_studenti", $elenco_studenti);
        //}}} </editor-fold>
        //}}} </editor-fold>
    }

    $pdf->SetFillColor(224, 235, 255);
    $periodo = 9;

    $larghezza_campi_aggiuntivi = 0;

    if ($stampa_voto_ammissione == "SI") {
        $larghezza_campi_aggiuntivi = $larghezza_campi_aggiuntivi + 20;
    }

    if ($stampa_voti_prove == "SI") {
        $larghezza_campi_aggiuntivi = $larghezza_campi_aggiuntivi + 20;
    }

    if ($stampa_voto_qualifica == "SI") {
        $larghezza_campi_aggiuntivi = $larghezza_campi_aggiuntivi + 20;
    }

    if ($stampa_dicitura_ammissione == "SI") {
        $larghezza_campi_aggiuntivi = $larghezza_campi_aggiuntivi + 40;
    }

    if ($stampa_crediti == "SI") {
        $larghezza_campi_aggiuntivi = $larghezza_campi_aggiuntivi + 30;
    }

    if ($stampa_annotazioni == "SI") {
        $larghezza_annotazioni = 25;
        $larghezza_campi_aggiuntivi = $larghezza_campi_aggiuntivi + $larghezza_annotazioni;
    }

    $larghezza_nomi = 35;

    $elenco_materie = [];

    if ($form_stato == 'amministratore') {
        if ($multi_classe_pagelle == 'SI') {
            $elenco_materie_vecchio = estrai_materie_multi_classe((int) $id_classe);
        } else {
            $elenco_materie_vecchio = estrai_materie_classe((int) $id_classe);
        }
    } elseif ($form_stato == 'professore') {
        $coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $current_user);

        if ($coordinatore == "SI") {
            if ($multi_classe_pagelle == 'SI') {
                $elenco_materie_vecchio = estrai_materie_multi_classe((int) $id_classe);
            } else {
                $elenco_materie_vecchio = estrai_materie_classe((int) $id_classe);
            }
        } else {
            if ($multi_classe_pagelle == 'SI') {
                $elenco_materie_vecchio = estrai_materie_multi_classe_del_professore((int) $id_classe, (int) $current_user);
            } else {
                $elenco_materie_vecchio = estrai_materie_classe_del_professore((int) $id_classe, (int) $current_user);
            }
        }
    }

    foreach ($elenco_materie_vecchio as $materia) {
        if ($materia['in_media_pagelle'] != 'NV') {
            $significati_voti[$materia["id_materia"]]["id_materia"] = $materia["id_materia"];
            $significati_voto = estrai_significati_voti_pagelle($materia["tipo_valutazione"], "solo_abilitati", $periodo);
            $significati_voti[$materia["id_materia"]]["significati_voto_pagelle"] = $significati_voto;
            $elenco_materie[] = $materia;
        }
    }

    $elenco_voti_pagelline_totale = estrai_voti_tabellone_pagellina_classe_finale((int) $id_classe, $periodo, (int) $current_user);

    $tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);

    //################## Ciclo di stampa ####################
    if ($param_tabellone_qualifiche_tipo_periodo == 'ammissione_completa') {
        //{{{ <editor-fold defaultstate="collapsed" desc="intestazione">
        $pdf->SetFont('helvetica', 'B', 12);

        if ($tipo_periodo == "ammissione_completa") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE AMMISSIONE AD ESAMI DI QUALIFICA', 0, 1, 'L');
        } else {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ESAMI DI QUALIFICA', 0, 1, 'L');
        }

        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 1, 'L');
        $pdf->Cell(0, 8, 'A.S. ' . $anno_scolastico, 0, 1, 'R');
        $altezza_ridotta += 16;
        $altezza_cella = ($altezza_pagina - ($altezza_ridotta - 5)) / intval(count($elenco_studenti_classe) + 1);

        $cont_stud = 0;
        $pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);

        //faccio il salto per lasciare lo spazio per le materie in verticale
        if ($verticale_orizzontale == "VERT") {
            $pdf->ln(20);
        }

        //intestazione
        $pdf->SetX(10);
        $pdf->CellFitScale(5, $altezza_cella, 'N°', 1, 0, 'C');
        $pdf->CellFitScale(30, $altezza_cella, 'STUDENTE', 1, 0, 'C');
        $y_riga = $pdf->GetY();

        if ($stampa_tutti_voti == "SI") {
            $numero_materie = count($elenco_materie);

            if ($numero_materie > 0) {
                $larghezza_colonna_materia = (((($larghezza_pagina - 20) - $larghezza_nomi) - $larghezza_campi_aggiuntivi) / $numero_materie);
                $dimensione_cella = $larghezza_colonna_materia;
            } else {
                $larghezza_colonna_materia = 100;
            }

            $cont_materia = 0;
            foreach ($elenco_materie as $materia) {
                if (($materia["in_media_pagelle"] == 'SI')) {
                    switch ($codice_descrizione) {
                    case 'C':
                        $testo_materia_scelto = $materia['codice'];
                        break;
                    case 'D':
                        $testo_materia_scelto = $materia['descrizione'];
                        break;
                    case 'S':
                        $testo_materia_scelto = $materia['nome_materia_sito'];
                        break;
                    case 'B':
                        $testo_materia_scelto = $materia['nome_materia_breve'];
                        break;
                    }

                    if ($verticale_orizzontale == "VERT") {
                        $x_base = $pdf->GetX();
                        $y_base = $pdf->GetY();
                        $pdf->SetXY($x_base, $y_base + 2);
                        $testo_materia = wordwrap($testo_materia_scelto, 10, "\n");
                        $righe_testo = explode("\n", $testo_materia);
                        $numero_righe = count($righe_testo);
                        $pdf->StartTransform();
                        $pdf->Rotate(90);
                        $pdf->MultiCell(70, $dimensione_cella, $testo_materia, 0, 'L', false, 1, '', '', true, 1, false, true, $dimensione_cella, 'M', true);
                        $pdf->SetXY($x_base, $y_base + 3);
                        $pdf->StopTransform();
                        $pdf->SetXY($x_base, $y_base);
                        $pdf->SetXY($x_base + $dimensione_cella, $y_base);
                    } elseif ($verticale_orizzontale == "ORIZZ") {
                        $testo_materia = $testo_materia_scelto;
                        $pdf->CellFitScale($dimensione_cella, 4, $testo_materia, 'LTR', 0, 'C');
                    }
                }
            }

            if ($verticale_orizzontale == "VERT") {
                $pdf->CellFitScale(0, $altezza_cella - 3, '', 0, 1, 'C');
                $pdf->CellFitScale(5, $altezza_cella, '', 0, 0, 'C');
                $pdf->CellFitScale(30, $altezza_cella, '', 0, 0, 'C');
                $pdf->SetXY($pdf->GetX(), $pdf->GetY() - 3);
            } else {
                $pdf->CellFitScale(0, ($altezza_cella / 2) - 1, '', 0, 1, 'C');
                $pdf->CellFitScale(5, $altezza_cella, '', 0, 0, 'C');
                $pdf->CellFitScale(30, $altezza_cella, '', 0, 0, 'C');
                $pdf->SetXY($pdf->GetX(), $pdf->GetY() - 1);
            }

            foreach ($elenco_materie as $materia) {
                if ($materia["in_media_pagelle"] == 'SI') {
                    if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                        $pdf->CellFitScale($larghezza_colonna_materia, 3, 'V', 1, 0, 'C');
                    } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                        $pdf->CellFitScale($larghezza_colonna_materia, 3, 'A', 1, 0, 'C');
                    } else {
                        $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'V', 1, 0, 'C');
                        $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'A', 1, 0, 'C');
                    }
                }
            }
        }

        $x_riga = $pdf->GetX();

        if ($stampa_voto_ammissione == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(20, $altezza_cella, "VOTO AMMISSIONE", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }
        if ($stampa_crediti == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(30, $altezza_cella, "CREDITI", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }
        if ($stampa_dicitura_ammissione == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(40, $altezza_cella, "RISULTATO", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }
        if ($stampa_annotazioni == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale($larghezza_annotazioni, $altezza_cella, "ANNOTAZIONI", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }

        $pdf->CellFitScale(0, $altezza_temp, '', 0, 1, 'C');
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="ciclo studenti">
        for ($cont_stud = 0; $cont_stud < count($elenco_studenti_classe); $cont_stud++) {
            $id_studente = $elenco_studenti_classe[$cont_stud][0];
            $dati_studente = estrai_dati_studente((int) $id_studente);
            if ($dati_studente['sesso'] == 'F') {
                $suffisso_oa = 'a';
                $suffisso_OA = 'A';
            } else {
                $suffisso_oa = 'o';
                $suffisso_OA = 'O';
            }

            $elenco_voti_pagelline_qual = $elenco_voti_pagelline_totale[$id_studente];

            if (( ($dati_studente['data_ritiro'] > 0) && ($dati_studente['data_ritiro'] < mktime(0, 0, 0, 3, 16, $anno_fine)) && ( stripos($dati_studente['esito_corrente_calcolato'], 'ritirato') !== false || stripos($dati_studente['esito_corrente_calcolato'], 'trasferito') !== false ) && ($visualizza_ritirati == "NO") ) || ( ($dati_studente["ammesso_esame_qualifica"] == "NO") && ($tipo_periodo == "ammissione_completa") && ($visualizza_nomi_bocciati == "NO_RIGA") ) || ( ($dati_studente["voto_qualifica"] < 60) && ($tipo_periodo != "ammissione_completa") && ($visualizza_nomi_bocciati == "NO_RIGA") )
            ) {

            } else {
                $pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);
                $totale_voti = 0;
                if ($celle_colorate == 'SI') {
                    $pdf->SetFillColor(255, 255, 255);
                    $fill = 1;
                } else {
                    $fill = !$fill;
                }
                $tot_insuff = 0;

                foreach ($elenco_voti_pagelline_qual as $voto_pagellina) {
                    if (($voto_pagellina['voto_pagellina'] > $voto_minimo) && ($voto_pagellina['voto_pagellina'] < $voto_minimo_suff) && ($voto_pagellina['in_media_pagelle'] == "SI")
                    ) {
                        $tot_insuff++;
                    }
                }

                if ($tipo_periodo == "ammissione_completa") {
                    if ($visualizza_nomi_bocciati == "NO" && $dati_studente["ammesso_esame_qualifica"] == "NO") {
                        $pdf->CellFitScale(5, $altezza_cella, '', 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'L', $fill);
                    } else {
                        $pdf->CellFitScale(5, $altezza_cella, $dati_studente['registro'], 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, $dati_studente['cognome'] . ' ' . $dati_studente['nome'], 1, 0, 'L', $fill);
                    }
                } else {
                    if ($visualizza_nomi_bocciati == "NO" && $dati_studente["voto_qualifica"] < 60) {
                        $pdf->CellFitScale(5, $altezza_cella, '', 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'L', $fill);
                    } else {
                        $pdf->CellFitScale(5, $altezza_cella, $dati_studente['registro'], 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, $dati_studente['cognome'] . ' ' . $dati_studente['nome'], 1, 0, 'L', $fill);
                    }
                }

                if ($stampa_tutti_voti == "SI") {
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $cont_materia = 0;

                    foreach ($elenco_materie as $materia) {
                        foreach ($elenco_voti_pagelline_qual as $id_mat => $voto_pagellina) {
                            $trovato = false;

                            if ($materia["id_materia"] == $id_mat) {
                                $voto_selezionato = $voto_pagellina['voto_pagellina'];
                                $assenza_selezionata = $voto_pagellina['ore_assenza'];
                            }
                        }

                        $cont_materia++;

                        if (( $dati_studente["ammesso_esame_qualifica"] == "SI" && $tipo_periodo == "ammissione_completa" ) || ( $dati_studente["voto_qualifica"] >= 60 && $tipo_periodo <> "ammissione_completa" ) || ( $visualizza_bocciati == "SI" ) || !( $visualizza_ammesso_con_insufficienze == 'NO' && strpos(strtoupper($esito['esito']), 'AMMESSO SECONDO O.M.') !== false && $tot_insuff > 0)
                        ) {
                            if ($materia["in_media_pagelle"] == "SI") {
                                if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                                    if ($visualizza_nomi_bocciati == "NO" && $tot_insuff >= 2) {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, '', 1, 0, 'C', $fill);
                                    } else {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                    }
                                } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                                    if ($visualizza_nomi_bocciati == "NO" && $tot_insuff >= 2) {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, '', 1, 0, 'C', $fill);
                                    } else {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                    }
                                } else {
                                    if ($visualizza_nomi_bocciati == "NO" && $tot_insuff >= 2) {
                                        $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, "", 1, 0, 'C', $fill);
                                        $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, "", 1, 0, 'C', $fill);
                                    } else {
                                        $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                        $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                    }
                                }
                            }
                        } else {
                            if ($materia["in_media_pagelle"] == "SI") {
                                $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, '', 1, 0, 'C', $fill);
                            }
                        }

                        $assenza_selezionata = "";
                        $voto_selezionato = "";
                    }
                }

                if ($stampa_voto_ammissione == "SI") {
                    if ($visualizza_bocciati == "NO" && $dati_studente["ammesso_esame_qualifica"] == "NO") {
                        $pdf->CellFitScale(20, $altezza_cella, '', 1, 0, 'C', $fill);
                    } else {
                        $ammissione_temp = $dati_studente["voto_ammissione"];
                        $pdf->CellFitScale(20, $altezza_cella, $ammissione_temp, 1, 0, 'C', $fill);
                    }
                }
                if ($stampa_crediti == "SI") {
                    $crediti = $studente[0]["crediti_reintegrati_terza"] + $studente[0]["crediti_terza"];

                    if ($visualizza_bocciati == "SI") {
                        $pdf->CellFitScale(30, $altezza_cella, $crediti, 1, 0, 'C', $fill);
                    } else {
                        if ($visualizza_bocciati == "NO" && $studente[0]["voto_qualifica"] < 60) {
                            $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'C', $fill);
                        } else {
                            $pdf->CellFitScale(30, $altezza_cella, $crediti, 1, 0, 'C', $fill);
                        }
                    }
                }
                if ($tipo_periodo == "ammissione_completa") {
                    if ($stampa_dicitura_ammissione == "SI") {
                        if ($visualizza_nomi_bocciati == "SI") {
                            switch ($dati_studente["ammesso_esame_qualifica"]) {
                            case "--":
                                $stato_studente = "";
                                break;
                            case "SI":
                                $stato_studente = "AMMESS$suffisso_OA";
                                break;
                            case "NO":
                                $stato_studente = "NON AMMESS$suffisso_OA";
                                break;
                            }
                        } else {
                            switch ($dati_studente["ammesso_esame_qualifica"]) {
                            case "--":
                                $stato_studente = "";
                                break;
                            case "SI":
                                $stato_studente = "AMMESS$suffisso_OA";
                                break;
                            case "NO":
                                $stato_studente = "";
                                break;
                            }
                        }

                        if ($dati_studente["ritirato"] == "1") {
                            $stato_studente = "RITIRAT$suffisso_OA";
                        }

                        if ($dati_studente["ritirato"] == "2") {
                            $stato_studente = "TRASFERIT$suffisso_OA";
                        }

                        if ($dati_studente["ritirato"] == "3") {
                            $stato_studente = "RITIRAT$suffisso_OA D'UFFICIO";
                        }

                        if ($dati_studente["ritirato"] == "4") {
                            $stato_studente = "ISCRITT$suffisso_OA";
                        }

                        $pdf->CellFitScale(40, $altezza_cella, $stato_studente, 1, 0, 'C', $fill);
                    }
                } else {
                    if ($stampa_dicitura_ammissione == "SI") {
                        if ($visualizza_nomi_bocciati == "NO" && $dati_studente["voto_qualifica"] < 60) {
                            if ($dati_studente["voto_qualifica"] < 60) {
                                $pdf->CellFitScale(40, $altezza_cella, "", 1, 0, 'C', $fill);
                            } else {
                                $pdf->CellFitScale(40, $altezza_cella, "QUALIFICAT$suffisso_OA", 1, 0, 'C', $fill);
                            }
                        } else {
                            if ($dati_studente["voto_qualifica"] < 60) {
                                if ($dati_studente["ammesso_esame_qualifica"] == 'SI') {
                                    $pdf->CellFitScale(40, $altezza_cella, "NON QUALIFICAT$suffisso_OA", 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale(40, $altezza_cella, "NON AMMESS$suffisso_OA", 1, 0, 'C', $fill);
                                }
                            } else {
                                $pdf->CellFitScale(40, $altezza_cella, "QUALIFICAT$suffisso_OA", 1, 0, 'C', $fill);
                            }
                        }
                    }
                }
                if ($stampa_annotazioni == "SI") {
                    $pdf->CellFitScale($larghezza_annotazioni, $altezza_cella, '', 1, 0, 'C', $fill);
                }

                $pdf->CellFitScale(0, $altezza_cella, '', 0, 1, 'C');
            }
        }
        //}}} </editor-fold>
    } else {
        //{{{ <editor-fold defaultstate="collapsed" desc="intestazione">
        $pdf->SetFont('helvetica', 'B', 12);

        if ($tipo_periodo == "ammissione") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE AMMISSIONE AD ESAMI DI QUALIFICA', 0, 1, 'L');
        } else {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ESAMI DI QUALIFICA', 0, 1, 'L');
        }

        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 1, 'L');
        $pdf->Cell(0, 8, 'A.S. ' . $anno_scolastico, 0, 1, 'R');
        $altezza_ridotta += 16;
        $altezza_cella = ($altezza_pagina - ($altezza_ridotta - 5)) / intval(count($elenco_studenti_classe) + 1);

        $cont_stud = 0;
        $pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);

        //faccio il salto per lasciare lo spazio per le materie in verticale
        if ($verticale_orizzontale == "VERT") {
            $pdf->ln(20);
        }

        //intestazione
        $pdf->SetX(10);
        $pdf->CellFitScale(5, $altezza_cella, 'N°', 1, 0, 'C');
        $pdf->CellFitScale(30, $altezza_cella, 'STUDENTE', 1, 0, 'C');
        $y_riga = $pdf->GetY();

        if ($stampa_tutti_voti == "SI") {
            $numero_materie = count($elenco_materie);
            if ($numero_materie > 0) {
                $larghezza_colonna_materia = (((($larghezza_pagina - 20) - $larghezza_nomi) - $larghezza_campi_aggiuntivi) / $numero_materie);
                $dimensione_cella = $larghezza_colonna_materia;
            } else {
                $larghezza_colonna_materia = 100;
            }

            $cont_materia = 0;
            foreach ($elenco_materie as $materia) {
                switch ($codice_descrizione) {
                case 'C':
                    $testo_materia_scelto = $materia['codice'];
                    break;
                case 'D':
                    $testo_materia_scelto = $materia['descrizione'];
                    break;
                case 'S':
                    $testo_materia_scelto = $materia['nome_materia_sito'];
                    break;
                case 'B':
                    $testo_materia_scelto = $materia['nome_materia_breve'];
                    break;
                }

                if ($verticale_orizzontale == "VERT") {
                    $x_base = $pdf->GetX();
                    $y_base = $pdf->GetY();
                    $pdf->SetXY($x_base, $y_base + 3);
                    $testo_materia = wordwrap($testo_materia_scelto, 10, "\n");
                    $righe_testo = explode("\n", $testo_materia);
                    $numero_righe = count($righe_testo);
                    $pdf->StartTransform();
                    $pdf->Rotate(90);
                    $pdf->MultiCell(70, $dimensione_cella, $testo_materia, 0, 'L', false, 1, '', '', true, 1, false, true, $dimensione_cella, 'M', true);
                    $pdf->SetXY($x_base, $y_base + 3);
                    $pdf->StopTransform();
                    $pdf->SetXY($x_base, $y_base);
                    $pdf->CellFitScale($dimensione_cella, 4, '', 0, 0, 'C');
                    $pdf->SetXY($x_base + $dimensione_cella, $y_base);

                    $testo_excel .= $testo_materia_scelto . "	";
                } elseif ($verticale_orizzontale == "ORIZZ") {
                    $altezza_cella_mat = $stampa_tipo_valore_intestazione != 'NO' ? 4 : 7;
                    $testo_materia = $testo_materia_scelto;
                    $pdf->CellFitScale($dimensione_cella, $altezza_cella_mat, $testo_materia, 'LTR', 0, 'C');
                    $testo_excel .= $testo_materia_scelto . "	";
                }

                $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;
            }

            if ($stampa_tipo_valore_intestazione != 'NO') {
                $pdf->CellFitScale(0, $altezza_cella - 3, '', 0, 1, 'C');
                $pdf->CellFitScale(5, $altezza_cella, '', 0, 0, 'C');
                $pdf->CellFitScale(30, $altezza_cella, '', 0, 0, 'C');

                foreach ($elenco_materie as $materia) {
                    if ($materia["tipo_materia"] == "RELIGIONE") {
                        if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'V', 1, 0, 'C');
                        } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'A', 1, 0, 'C');
                        } else {
                            $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'V', 1, 0, 'C');
                            $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'A', 1, 0, 'C');
                        }
                    } elseif ($materia["tipo_materia"] == "CONDOTTA") {
                        if ($visualizza_assenze == "SOLO_ASSENZE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, '', 1, 0, 'C');
                        } else {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'V', 1, 0, 'C');
                        }
                    } else {
                        if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'V', 1, 0, 'C');
                        } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'A', 1, 0, 'C');
                        } else {

                            $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'V', 1, 0, 'C');
                            $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'A', 1, 0, 'C');
                        }
                    }
                }
            }
        } elseif ($stampa_tutti_voti == "SOLO_REL_E_CON") {
            $numero_materie = 2;
            $larghezza_colonna_materia = 40;
            $altezza_cella_mat = $stampa_tipo_valore_intestazione != 'NO' ? $altezza_cella - 3 : $altezza_cella;

            foreach ($elenco_materie as $materia) {
                if ($materia["tipo_materia"] == "CONDOTTA" || $materia["tipo_materia"] == "RELIGIONE") {
                    $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella_mat, $materia["descrizione"], 1, 0, 'C');
                }
            }

            if ($stampa_tipo_valore_intestazione != 'NO') {
                $pdf->CellFitScale(0, $altezza_cella - 3, '', 0, 1, 'C');
                $pdf->CellFitScale(5, $altezza_cella, '', 0, 0, 'C');
                $pdf->CellFitScale(30, $altezza_cella, '', 0, 0, 'C');

                foreach ($elenco_materie as $materia) {
                    if ($materia["tipo_materia"] == "RELIGIONE") {
                        if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'V', 1, 0, 'C');
                        } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'A', 1, 0, 'C');
                        } else {
                            $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'V', 1, 0, 'C');
                            $pdf->CellFitScale($larghezza_colonna_materia / 2, 3, 'A', 1, 0, 'C');
                        }
                    } elseif ($materia["tipo_materia"] == "CONDOTTA") {
                        if ($visualizza_assenze == "SOLO_ASSENZE") {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, '', 1, 0, 'C');
                        } else {
                            $pdf->CellFitScale($larghezza_colonna_materia, 3, 'V', 1, 0, 'C');
                        }
                    }
                }
            }
        }

        $x_riga = $pdf->GetX();

        if ($stampa_voto_ammissione == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(20, $altezza_cella, "VOTO AMMISSIONE", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }

        if ($stampa_voti_prove == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(10, $altezza_cella, "1^ Prova", 1, 0, 'C');
            $pdf->CellFitScale(10, $altezza_cella, "2^ Prova", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }

        if ($stampa_voto_qualifica == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(20, $altezza_cella, "VOTO QUALIFICA", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }

        if ($stampa_crediti == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(30, $altezza_cella, "CREDITI", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }

        if ($stampa_dicitura_ammissione == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale(40, $altezza_cella, "RISULTATO", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }

        if ($stampa_annotazioni == "SI") {
            $pdf->SetXY($x_riga, $y_riga);
            $pdf->CellFitScale($larghezza_annotazioni, $altezza_cella, "ANNOTAZIONI", 1, 0, 'C');
            $x_riga = $pdf->GetX();
            $altezza_temp = $altezza_cella;
        }

        $pdf->CellFitScale(0, $altezza_temp, '', 0, 1, 'C');
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="ciclo studenti">
        $cont_stud = 1;
        // estraggo i voti della pagella di fine anno
        $elenco_voti_pagelline_fa = [];

        $elenco_voti_pagelline_fa = estrai_voti_tabellone_pagellina_classe_rivisto((int) $id_classe, "9", $elenco_studenti_classe, (int) $current_user);
        foreach ($elenco_voti_pagelline_fa as $studente) {
            $dati_studente = [];
            $dati_studente = estrai_dati_studente_completi((int) $studente[0][0]);
            if ($dati_studente['sesso'] == 'F') {
                $suffisso_oa = 'a';
                $suffisso_OA = 'A';
            } else {
                $suffisso_oa = 'o';
                $suffisso_OA = 'O';
            }
            if (( ($dati_studente['data_ritiro'] > 0) && ($dati_studente['data_ritiro'] < mktime(0, 0, 0, 3, 16, $anno_fine)) && (stripos($dati_studente['esito_corrente_calcolato'], 'ritirato') !== false or stripos($dati_studente['esito_corrente_calcolato'], 'trasferito') !== false) && ($visualizza_ritirati == "NO") ) || ( ($studente[0]["ammesso_esame_qualifica"] == "NO") && ($tipo_periodo == "ammissione") && ($visualizza_nomi_bocciati == "NO_RIGA") ) || ( ($studente[0]["ammesso_esame_qualifica"] == "NO") && ($tipo_periodo == "qualifica") && ($param_tabellone_qualifiche_tipo_periodo == 'qualifica') ) || ( (($studente[0]["voto_qualifica"] < 60) || ($studente[0]["ammesso_esame_qualifica"] == "NO")) && ($tipo_periodo != "ammissione") && ($visualizza_nomi_bocciati == "NO_RIGA") )
            ) {

            } else {
                $pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);
                $totale_voti = 0;
                if ($celle_colorate == 'SI') {
                    $pdf->SetFillColor(255, 255, 255);
                    $fill = 1;
                } else {
                    $fill = !$fill;
                }

                $tot_insuff = 0;

                foreach ($studente as $voto_pagellina) {
                    if (($voto_pagellina[8] > $voto_minimo) && ($voto_pagellina[8] < $voto_minimo_suff) && ($voto_pagellina['in_media_pagelle'] == "SI")
                    ) {
                        $tot_insuff++;
                    }
                }

                if ($tipo_periodo == "ammissione") {
                    if (($visualizza_nomi_bocciati == "NO") && ($studente[0]["ammesso_esame_qualifica"] == "NO")) {
                        $pdf->CellFitScale(5, $altezza_cella, '', 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'L', $fill);
                    } else {
                        $pdf->CellFitScale(5, $altezza_cella, $studente[0][1], 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, $studente[0][3] . ' ' . $studente[0][2], 1, 0, 'L', $fill);
                    }
                } else {
                    if ($visualizza_nomi_bocciati == "NO" && $studente[0]["voto_qualifica"] < 60) {
                        $pdf->CellFitScale(5, $altezza_cella, '', 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'L', $fill);
                    } else {
                        $pdf->CellFitScale(5, $altezza_cella, $studente[0][1], 1, 0, 'R', $fill);
                        $pdf->CellFitScale(30, $altezza_cella, $studente[0][3] . ' ' . $studente[0][2], 1, 0, 'L', $fill);
                    }
                }

                if ($stampa_tutti_voti == "SI") {
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $cont_materia = 0;

                    foreach ($elenco_materie as $materia) {
                        foreach ($studente as $voto_pagellina) {
                            $trovato = false;

                            if ($materia["id_materia"] == $voto_pagellina[10]) {
                                $assenza_selezionata = stampa_ore_o_minuti($voto_pagellina[14]);
                                switch ($tipo_voto_stampa) {
                                case "scheda":
                                    $voto_selezionato = $voto_pagellina[8];
                                    break;
                                case "codice":
                                    $significati_tmp = estrai_significati_voti_specifici($voto_pagellina[8], $voto_pagellina["tipo_valutazione"]);
                                    $voto_selezionato = decode($significati_tmp["codice_pagella"]);
                                    break;
                                case "descrizione":
                                    $significati_tmp = estrai_significati_voti_specifici($voto_pagellina[8], $voto_pagellina["tipo_valutazione"]);
                                    $voto_selezionato = decode($significati_tmp["valore_pagella"]);
                                    break;
                                default:
                                    $voto_selezionato = $voto_pagellina[8];
                                    break;
                                }

                                if ($visualizza_bocciati == 'NO' && $studente[0]["ammesso_esame_qualifica"] == "NO" && $tipo_periodo == "ammissione"
                                ) {
                                    $voto_selezionato = '';
                                }
                            }
                        }

                        $cont_materia++;

                        if (( $studente[0]["ammesso_esame_qualifica"] == "SI" && $tipo_periodo == "ammissione" ) || ( $studente[0]["voto_qualifica"] >= 60 && $tipo_periodo <> "ammissione" ) || ( $visualizza_bocciati == "SI" ) || !( $visualizza_ammesso_con_insufficienze == 'NO' && strpos(strtoupper($esito['esito']), 'AMMESSO SECONDO O.M.') !== false && $tot_insuff > 0)
                        ) {
                            if ($materia["tipo_materia"] == "RELIGIONE") {
                                if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                                    $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                                    $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                    $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                }
                            } elseif ($materia["tipo_materia"] == "CONDOTTA") {
                                if ($visualizza_assenze == "SOLO_ASSENZE") {
                                    $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, '', 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                }
                            } else {
                                if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                                    $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                                    $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                    $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                }
                            }
                        } else {
                            $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, '', 1, 0, 'C', $fill);
                        }

                        $assenza_selezionata = "";
                        $voto_selezionato = "";
                    }
                } elseif ($stampa_tutti_voti == "SOLO_REL_E_CON") {
                    $cont_materia = 0;

                    foreach ($elenco_materie as $materia) {
                        if ($materia["tipo_materia"] == "CONDOTTA" || $materia["tipo_materia"] == "RELIGIONE") {
                            foreach ($studente as $voto_pagellina) {
                                $trovato = false;

                                if ($materia["id_materia"] == $voto_pagellina[10]) {
                                    $assenza_selezionata = stampa_ore_o_minuti($voto_pagellina[14]);
                                    switch ($tipo_voto_stampa) {
                                    case "scheda":
                                        $voto_selezionato = $voto_pagellina[8];
                                        break;
                                    case "codice":
                                        $significati_tmp = estrai_significati_voti_specifici($voto_pagellina[8], $voto_pagellina["tipo_valutazione"]);
                                        $voto_selezionato = decode($significati_tmp["codice_pagella"]);
                                        break;
                                    case "descrizione":
                                        $significati_tmp = estrai_significati_voti_specifici($voto_pagellina[8], $voto_pagellina["tipo_valutazione"]);
                                        $voto_selezionato = decode($significati_tmp["valore_pagella"]);
                                        break;
                                    default:
                                        $voto_selezionato = $voto_pagellina[8];
                                        break;
                                    }
                                }
                            }

                            $cont_materia++;

                            if (( $studente[0]["ammesso_esame_qualifica"] == "SI" && $tipo_periodo == "ammissione" ) || ( $studente[0]["voto_qualifica"] >= 60 && $tipo_periodo <> "ammissione" ) || ( $visualizza_bocciati == "SI" ) || !( $visualizza_ammesso_con_insufficienze == 'NO' && strpos(strtoupper($esito['esito']), 'AMMESSO SECONDO O.M.') !== false && $tot_insuff > 0)
                            ) {
                                if ($materia["tipo_materia"] == "RELIGIONE") {
                                    if ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                    } elseif ($visualizza_assenze == "SOLO_ASSENZE") {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                    } else {
                                        $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                        $pdf->CellFitScale($larghezza_colonna_materia / 2, $altezza_cella, stampa_ore_o_minuti($assenza_selezionata, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                    }
                                } elseif ($materia["tipo_materia"] == "CONDOTTA") {
                                    if ($visualizza_assenze == "SOLO_ASSENZE") {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, '', 1, 0, 'C', $fill);
                                    } else {
                                        $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, $voto_selezionato, 1, 0, 'C', $fill);
                                    }
                                }
                            } else {
                                $pdf->CellFitScale($larghezza_colonna_materia, $altezza_cella, '', 1, 0, 'C', $fill);
                            }

                            $cont_materia++;
                            $assenza_selezionata = "";
                            $voto_selezionato = "";
                        }
                    }
                }

                if ($stampa_voto_ammissione == "SI") {
                    if ($visualizza_bocciati == "NO" && $studente[0]["ammesso_esame_qualifica"] == "NO") {
                        $pdf->CellFitScale(20, $altezza_cella, '', 1, 0, 'C', $fill);
                    } else {
                        $ammissione_temp = $studente[0]["voto_ammissione"];
                        $pdf->CellFitScale(20, $altezza_cella, $ammissione_temp, 1, 0, 'C', $fill);
                    }
                }

                if ($stampa_voti_prove == "SI") {
                    if ($visualizza_bocciati == "SI") {
                        $pdf->CellFitScale(10, $altezza_cella, $studente[0]["voto_esame_sc1_qual"], 1, 0, 'C', $fill);
                        $pdf->CellFitScale(10, $altezza_cella, $studente[0]["voto_esame_sc2_qual"], 1, 0, 'C', $fill);
                    } else {
                        if ($visualizza_bocciati == "NO" && $studente[0]["voto_qualifica"] < 60) {
                            $pdf->CellFitScale(10, $altezza_cella, '', 1, 0, 'C', $fill);
                            $pdf->CellFitScale(10, $altezza_cella, '', 1, 0, 'C', $fill);
                        } else {
                            $pdf->CellFitScale(10, $altezza_cella, $studente[0]["voto_esame_sc1_qual"], 1, 0, 'C', $fill);
                            $pdf->CellFitScale(10, $altezza_cella, $studente[0]["voto_esame_sc2_qual"], 1, 0, 'C', $fill);
                        }
                    }
                }

                if ($stampa_voto_qualifica == "SI") {
                    if ($studente[0]["voto_qualifica"] > 100) {
                        $studente[0]["voto_qualifica"] = 100;
                    }

                    if ($visualizza_bocciati == "SI") {
                        if ($studente[0]["ammesso_esame_qualifica"] == "SI") {
                            $pdf->CellFitScale(20, $altezza_cella, $studente[0]["voto_qualifica"], 1, 0, 'C', $fill);
                        } else {
                            $pdf->CellFitScale(20, $altezza_cella, '', 1, 0, 'C', $fill);
                        }
                    } else {
                        if ($visualizza_bocciati == "NO" && $studente[0]["voto_qualifica"] < 60) {
                            $pdf->CellFitScale(20, $altezza_cella, '', 1, 0, 'C', $fill);
                        } else {
                            if ($studente[0]["ammesso_esame_qualifica"] == "SI") {
                                $pdf->CellFitScale(20, $altezza_cella, $studente[0]["voto_qualifica"], 1, 0, 'C', $fill);
                            } else {
                                $pdf->CellFitScale(20, $altezza_cella, '', 1, 0, 'C', $fill);
                            }
                        }
                    }
                }

                if ($stampa_crediti == "SI") {
                    $crediti = $studente[0]["crediti_reintegrati_terza"] + $studente[0]["crediti_terza"];

                    if ($visualizza_bocciati == "SI") {
                        $pdf->CellFitScale(30, $altezza_cella, $crediti, 1, 0, 'C', $fill);
                    } else {
                        if ($visualizza_bocciati == "NO" && $studente[0]["voto_qualifica"] < 60) {
                            $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'C', $fill);
                        } else {
                            $pdf->CellFitScale(30, $altezza_cella, $crediti, 1, 0, 'C', $fill);
                        }
                    }
                }

                if ($tipo_periodo == "ammissione") {
                    if ($stampa_dicitura_ammissione == "SI") {
                        if (($dati_studente["mat_esito"]['esito'] == 'Ritirato' || $dati_studente["mat_esito"]['esito'] == 'Trasferito' || $dati_studente["mat_esito"]['esito'] == 'Ritirato d\'ufficio') && $dati_studente["mat_esito"]['data_ritiro'] <= mktime(0, 0, 0, 3, 15, $anno_fine)
                        ) {
                            if ($dati_studente["mat_esito"]['esito'] == 'Trasferito') {
                                $stato_studente = 'Trasferit' . $suffisso_oa . ' prima del 15/3';
                            } else {
                                $stato_studente = 'Ritirat' . $suffisso_oa . ' prima del 15/3';
                            }
                        } elseif (strpos($dati_studente["mat_esito"]['esito'], 'perso') !== false) {
                            $stato_studente = 'Ritirat' . $suffisso_oa . ' prima del 15/3';
                        } elseif ($dati_studente["mat_esito"]['esito'] == 'Frequenza anno estero') {
                            $stato_studente = 'Frequenza anno all\'estero';
                        } else {
                            if ($visualizza_nomi_bocciati == "SI") {
                                switch ($studente[0]["ammesso_esame_qualifica"]) {
                                case "--":
                                    $stato_studente = "";
                                    break;
                                case "SI":
                                    $stato_studente = "AMMESS$suffisso_OA";
                                    break;
                                case "NO":
                                    $stato_studente = "NON AMMESS$suffisso_OA";
                                    break;
                                }
                            } else {
                                switch ($studente[0]["ammesso_esame_qualifica"]) {
                                case "--":
                                    $stato_studente = "";
                                    break;
                                case "SI":
                                    $stato_studente = "AMMESS$suffisso_OA";
                                    break;
                                case "NO":
                                    $stato_studente = "";
                                    break;
                                }
                            }
                        }

                        $pdf->CellFitScale(40, $altezza_cella, $stato_studente, 1, 0, 'C', $fill);
                    }
                } else {
                    if ($stampa_dicitura_ammissione == "SI") {
                        if ($visualizza_nomi_bocciati == "NO" && $studente[0]["voto_qualifica"] < 60) {
                            if ($studente[0]["voto_qualifica"] < 60) {
                                $pdf->CellFitScale(40, $altezza_cella, "", 1, 0, 'C', $fill);
                            } else {
                                if ($studente[0]["ammesso_esame_qualifica"] == "SI") {
                                    $pdf->CellFitScale(40, $altezza_cella, "QUALIFICAT$suffisso_OA", 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale(40, $altezza_cella, "NON AMMESS$suffisso_OA", 1, 0, 'C', $fill);
                                }
                            }
                        } else {
                            if ($studente[0]["voto_qualifica"] < 60) {
                                if ($studente[0]["ammesso_esame_qualifica"] == "SI") {
                                    $pdf->CellFitScale(40, $altezza_cella, "NON QUALIFICAT$suffisso_OA", 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale(40, $altezza_cella, "NON AMMESS$suffisso_OA", 1, 0, 'C', $fill);
                                }
                            } else {
                                if ($studente[0]["ammesso_esame_qualifica"] == "SI") {
                                    $pdf->CellFitScale(40, $altezza_cella, "QUALIFICAT$suffisso_OA", 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale(40, $altezza_cella, "NON AMMESS$suffisso_OA", 1, 0, 'C', $fill);
                                }
                            }
                        }
                    }
                }
                if ($stampa_annotazioni == "SI") {
                    $pdf->CellFitScale($larghezza_annotazioni, $altezza_cella, '', 1, 0, 'C', $fill);
                }
                $pdf->CellFitScale(0, $altezza_cella, '', 0, 1, 'C');
                $cont_stud++;
            }
        }
        //}}} </editor-fold>
    }
    //}}} </editor-fold>
} elseif ($terze_medie_ammissione == 'SI') {
    //{{{ <editor-fold defaultstate="collapsed" desc="stampa in caso sia ammissione all'esame di terza media">
    $pdf->SetFillColor(224, 235, 255);
    $periodo = 29;

    $larghezza_nomi = 55;

    $elenco_studenti = estrai_studenti_classe((int) $id_classe);

    //################## Ciclo di stampa ####################
    //{{{ <editor-fold defaultstate="collapsed" desc="intestazione">
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE AMMISSIONE AD ESAMI DI STATO', 0, 1, 'L');
    $testo_excel .= 'TABELLONE AMMISSIONE AD ESAMI DI STATO' . chr(13) . chr(10);

    $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . ' ' . $dati_classe[5], 0, 1, 'L');
    $testo_excel .= 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . ' ' . $dati_classe[5] . chr(13) . chr(10);

    $altezza_ridotta += 16;
    $altezza_cella = ($altezza_pagina - ($altezza_ridotta - 5)) / intval(count($elenco_studenti_classe) + 1);

    $pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);

    //intestazione
    $pdf->SetX(10);
    $pdf->CellFitScale(5, $altezza_cella, 'N°', 1, 0, 'C');

    if ($orientamento_pagina != 'P' && $formato_pagina == 'A3') {
        $larghezza_cella = 120;
    } elseif ($orientamento_pagina != 'P' && $formato_pagina == 'A4') {
        $larghezza_cella = 80;
    } elseif ($orientamento_pagina == 'P' && $formato_pagina == 'A3') {
        $larghezza_cella = 80;
    } else {
        $larghezza_cella = 50;
    }

    $pdf->CellFitScale($larghezza_cella, $altezza_cella, 'STUDENTE', 1, 0, 'C');
    $y_riga = $pdf->GetY();
    $x_riga = $pdf->GetX();

    if ($stampa_risultato != 'SI_SENZA_VALORI') {
        $pdf->SetXY($x_riga, $y_riga);
        $pdf->CellFitScale($larghezza_cella, $altezza_cella, 'VOTO AMMISSIONE', 1, 0, 'C');
        $x_riga = $pdf->GetX();
        $altezza_temp = $altezza_cella;
    }

    $pdf->SetXY($x_riga, $y_riga);
    $pdf->CellFitScale($larghezza_cella, $altezza_cella, 'RISULTATO', 1, 0, 'C');
    $x_riga = $pdf->GetX();
    $altezza_temp = $altezza_cella;

    $pdf->CellFitScale(0, $altezza_temp, '', 0, 1, 'C');

    $testo_excel .= 'N°	STUDENTE	VOTO AMMISSIONE	RISULTATO' . chr(13) . chr(10);
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="ciclo studenti">
    $cont_stud = 1;

    foreach ($elenco_studenti as $studente) {
        if ($celle_colorate == 'SI') {
            $pdf->SetFillColor(255, 255, 255);
            $fill = 1;
        } else {
            $fill = !$fill;
        }
        $dati_studente = estrai_dati_studente_completi((int) $studente['id_studente']);
        if ($dati_studente['sesso'] == 'F') {
            $suffisso_oa = 'a';
            $suffisso_OA = 'A';
        } else {
            $suffisso_oa = 'o';
            $suffisso_OA = 'O';
        }

        if ($dati_studente['esito'] != 'Trasferito' && $dati_studente['esito'] != 'Ritirato') {
            if ($visualizza_nomi_bocciati == 'NO' && $studente['esito_terza_media'] == 'NO') {
                /* $pdf->CellFitScale(5, $altezza_cella, '', 1, 0, 'R', $fill);
                  $pdf->CellFitScale(50, $altezza_cella, '', 1, 0, 'L', $fill);
                  $pdf->CellFitScale(50, $altezza_cella, '' , 1, 0, 'C', $fill);
                  $pdf->CellFitScale(50, $altezza_cella, '', 1, 0, 'C', $fill);
                  $pdf->CellFitScale(0, $altezza_cella, '', 0, 1, 'C'); */
                $cont_stud++;
            } else {
                $pdf->CellFitScale(5, $altezza_cella, $studente['registro'], 1, 0, 'R', $fill);
                $pdf->CellFitScale($larghezza_cella, $altezza_cella, $studente['cognome'] . ' ' . $studente['nome'], 1, 0, 'L', $fill);

                $voto_ammissione = $stampa_risultato == 'SI_CON_VALORI' ? $studente['voto_ammissione_medie'] : '';

                if ($stampa_risultato != 'SI_SENZA_VALORI') {
                    $pdf->CellFitScale($larghezza_cella, $altezza_cella, $voto_ammissione, 1, 0, 'C', $fill);
                }

                $testo_excel .= $studente['registro'] . '	' . $studente['cognome'] . ' ' . $studente['nome'] . '	' . $voto_ammissione . '	';

                if ($studente['esito_terza_media'] == 'SI') {
                    $esito_da_stampare = "AMMESS$suffisso_OA ALL'ESAME DI STATO";
                } elseif ($studente['esito_terza_media'] == 'NO') {
                    $esito_da_stampare = "NON AMMESS$suffisso_OA ALL'ESAME DI STATO";
                } else {
                    $esito_da_stampare = '';
                }

                $pdf->CellFitScale($larghezza_cella, $altezza_cella, $esito_da_stampare, 1, 1, 'C', $fill);
                $testo_excel .= $esito_da_stampare . chr(13) . chr(10);
                $cont_stud++;
            }
        }
    }
    //}}} </editor-fold>
    //}}} </editor-fold>
} else {
    //{{{ <editor-fold defaultstate="collapsed" desc="intestazione e inizializzazione parametri per stampa tabelloni generici">
    $pdf->SetFont('helvetica', '', 12);

    if (((intval($periodo) >= 1) && (intval($periodo) <= 6)) || ((intval($periodo) >= 21) && (intval($periodo) <= 26))) {
        if ($visualizza_assenze == "SOLO_ASSENZE") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ASSENZE INFRA-QUADRIMESTRALE', 0, 1, 'L');
            $testo_excel .= 'TABELLONE ASSENZE INFRA-QUADRIMESTRALE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        } else {
            $pdf->Cell($larghezza_pagina - 40, 8, $titolo_stampa . ' INFRA-QUADRIMESTRALE', 0, 1, 'L');
            $testo_excel .= $titolo_stampa . ' INFRA-QUADRIMESTRALE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        }

        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
        $altezza_ridotta += 16;
    }

    if ((intval($periodo) == 7) || (intval($periodo) == 27)) {
        if ($visualizza_assenze == "SOLO_ASSENZE") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ASSENZE PRIMO QUADRIMESTRE/TRIMESTRE', 0, 1, 'L');
            $testo_excel .= 'TABELLONE ASSENZE PRIMO QUADRIMESTRE/TRIMESTRE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        } else {
            $pdf->Cell($larghezza_pagina - 40, 8, $titolo_stampa . ' PRIMO QUADRIMESTRE/TRIMESTRE', 0, 1, 'L');
            $testo_excel .= $titolo_stampa . ' PRIMO QUADRIMESTRE/TRIMESTRE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        }
        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
        $altezza_ridotta += 16;
    }

    if ((intval($periodo) == 8) || (intval($periodo) == 28)) {
        if ($visualizza_assenze == "SOLO_ASSENZE") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ASSENZE SECONDO TRIMESTRE', 0, 1, 'L');
            $testo_excel .= 'TABELLONE ASSENZE SECONDO TRIMESTRE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        } else {
            $pdf->Cell($larghezza_pagina - 40, 8, $titolo_stampa . ' SECONDO TRIMESTRE', 0, 1, 'L');
            $testo_excel .= $titolo_stampa . ' SECONDO TRIMESTRE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        }
        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
        $altezza_ridotta += 16;
    }

    if ((intval($periodo) == 9) || (intval($periodo) == 29)) {
        if ($visualizza_assenze == "SOLO_ASSENZE") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ASSENZE DI FINE ANNO', 0, 1, 'L');
            $testo_excel .= 'TABELLONE ASSENZE DI FINE ANNO  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        } else {
            if (($dati_classe['tipo_indirizzo'] == '3') && ($dati_classe['classe'] == 3)) {
                $pdf->Cell($larghezza_pagina - 40, 8, $titolo_stampa . ' PER L\'AMMISSIONE ALL\'ESAME DI MAESTRO D\'ARTE', 0, 1, 'L');
                $testo_excel .= $titolo_stampa . ' PER L\'AMMISSIONE ALL\'ESAME DI MAESTRO D\'ARTE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
            } else {
                $pdf->Cell($larghezza_pagina - 40, 8, $titolo_stampa . ' DI FINE ANNO', 0, 1, 'L');
                $testo_excel .= $titolo_stampa . ' DI FINE ANNO  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
            }
        }
        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
        $altezza_ridotta += 16;
    }

    if (intval($periodo) == 10) {
        if ($visualizza_assenze == "SOLO_ASSENZE") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ASSENZE PROVE STRUTTURATE', 0, 1, 'L');
            $testo_excel .= 'TABELLONE ASSENZE PROVE STRUTTURATE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        } else {
            $pdf->Cell($larghezza_pagina - 40, 8, $titolo_stampa . ' PROVE STRUTTURATE', 0, 1, 'L');
            $testo_excel .= $titolo_stampa . ' PROVE STRUTTURATE --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        }
        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
        $altezza_ridotta += 16;
    }

    if (intval($periodo) == 11) {
        if ($visualizza_assenze == "SOLO_ASSENZE") {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE ASSENZE ESAMI DI LICENZA PER MAESTRO D\'ARTE', 0, 1, 'L');
            $testo_excel .= 'TABELLONE ASSENZE ESAMI DI LICENZA PER MAESTRO D\'ARTE  --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        } else {
            $pdf->Cell($larghezza_pagina - 40, 8, 'TABELLONE RISULTATI ESAMI DI LICENZA MAESTRO D\'ARTE', 0, 1, 'L');
            $testo_excel .= 'TABELLONE RISULTATI ESAMI DI LICENZA MAESTRO D\'ARTE --  ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
        }
        $pdf->Cell($larghezza_pagina - 40, 8, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
        $altezza_ridotta += 16;
    }

    $pdf->Cell(0, 8, 'A.S. ' . $anno_scolastico, 0, 1, 'R');
    $altezza_ridotta += 8;
    $testo_excel .= 'A.S. ' . $anno_scolastico . chr(13) . chr(10);
    $max_Y = $pdf->GetY();

    if ($verticale_orizzontale == 'VERT') {
        $pdf->ln(28);
    } else {
        $pdf->ln(3);
    }

    $altezza_ridotta += 15;

    $pdf->SetFont('helvetica', 'B', 6);

    $tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);

    //estraggo solo le materie del primo studente
    $multi_classe_pagelle = estrai_parametri_singoli("MULTI_CLASSE_PAGELLE", (int) $id_classe, 'classe');

    if ($multi_classe_pagelle == "SI") {
        $elenco_materie_classe = estrai_materie_multi_classe((int) $id_classe);
    } else {
        $elenco_materie_classe = estrai_materie_classe((int) $id_classe);
    }

    $elenco_materie_tmp = [];

    if (is_array($elenco_materie_classe)) {
        foreach ($elenco_materie_classe as $chiave => $singola_materia) {
            if ($singola_materia['in_media_pagelle'] != 'NV') {
                $elenco_materie_tmp[$chiave] = $singola_materia;
            }
        }
    }

    $elenco_materie_classe = $elenco_materie_tmp;

    $elenco_voti_pagelline_totale = estrai_voti_tabellone_pagellina_classe_finale((int) $id_classe, $periodo, (int) $current_user);

    if (is_array($elenco_voti_pagelline_totale)) {
        foreach ($elenco_voti_pagelline_totale as $singolo_studente) {
            $elenco_voti_pagelline = $singolo_studente;
        }
    }

    $tot_larghezza_usata = 35;
    $pdf->Cell(5, 4, 'N°', 'LTR', 0, 'C');

    $pdf->Cell(30, 4, 'Nominativo', 'LTR', 0, 'C');

    $testo_excel .= 'N°	Nominativo	';
    $tot_celle = ($stampa_tutti_voti == 'SI' || $stampa_tutti_voti == 'SOLO_RECUPERI') ? count($elenco_materie_classe) : 0.5;

    if ($visualizza_assenze == "SOLO_ASSENZE") {
        $tot_celle = $tot_celle + 1;
    }

    if ($visualizza_assenze == 'SOLO_TOTALE') {
        $tot_celle = $tot_celle + 1;
    }

    if ($visualizza_assenze == 'SI') {
        $tot_celle = $tot_celle + 1;
    }

    if ($visualizza_assenze == 'PERCENTUALE') {
        $tot_celle = $tot_celle + 1;
    }

    if ($stampa_medie == "SI" || $stampa_medie == "SI_TOT") {
        $tot_celle = $tot_celle + 1;
    }

    if ($stampa_medie_provenienza == "SI") {
        $tot_celle = $tot_celle + 3;
    }

    if ($stampa_crediti == "SI") {
        $tot_celle = $tot_celle + 2;
    }

    if ($stampa_crediti_totali == "SI") {
        $tot_celle = $tot_celle + 0.5;
    }

    //    if($stampa_crediti_totali_om_2022 == "SI")
    //    {
    //        $tot_celle = $tot_celle + 0.5;
    //    }

    if ($stampa_risultato != "NO") {
        $tot_celle = $tot_celle + 3;
        $divisione_cella--;
    }

    if ($stampa_debiti_4a == "SI") {
        $tot_celle = $tot_celle + 3;
        $divisione_cella--;
    }

    if ($stampa_debiti_4b == "SI") {
        $tot_celle = $tot_celle + 3;
        $divisione_cella--;
    }

    if ($stampa_annotazioni == "SI") {
        $tot_celle = $tot_celle + 3;
    }


    if ($tot_celle > 0) {
        $dimensione_cella = ($larghezza_pagina - 60) / $tot_celle;
    } else {
        $dimensione_cella = 30;
    }
    $dimensione_mezza_cella = $dimensione_cella / 2;

    if (intval(count($elenco_studenti_classe)) > 0) {
        $altezza_cella = ($altezza_pagina - ($altezza_ridotta - 5)) / intval(count($elenco_studenti_classe));
        if ($altezza_cella < 3
            //&& ($stampa_debiti_4a == "SI" || $stampa_debiti_4b == "SI")
        ) {
            $altezza_cella = 3;
        }
    } else {
        $altezza_cella = 10;
    }

    $pdf->SetFont('helvetica', '', $dimensione_font_materie);

    if ($dimensione_font_materie >= 9) {
        $divisione_cella--;
    }

    if ($dimensione_font_materie >= 10) {
        $divisione_cella--;
        $divisione_cella--;
    }

    if ($dimensione_font_materie >= 11) {
        $divisione_cella--;
    }

    if ($stampa_tutti_voti == "SI" || $stampa_tutti_voti == 'SOLO_RECUPERI') {
        //{{{ <editor-fold defaultstate="collapsed" desc="stampo l'intestazione delle materie">
        foreach ($elenco_materie_classe as $cont => $singola_materia) {
            if (($periodo != '11') || ( ($periodo == '11') && ($singola_materia['in_media_pagelle'] == 'SI') )
            ) {
                switch ($codice_descrizione) {
                case 'C':
                    $testo_materia_scelto = $singola_materia['codice'];
                    break;
                case 'D':
                    $testo_materia_scelto = $singola_materia['descrizione'];
                    break;
                case 'S':
                    $testo_materia_scelto = $singola_materia['nome_materia_sito'];
                    break;
                case 'B':
                    $testo_materia_scelto = $singola_materia['nome_materia_breve'];
                    break;
                }
                if ($verticale_orizzontale == "VERT") {
                    $x_base = $pdf->GetX();
                    $y_base = $pdf->GetY();
                    $pdf->SetXY($x_base, $y_base + 3);
                    $testo_materia = wordwrap($testo_materia_scelto, 10, "\n");
                    $righe_testo = explode("\n", $testo_materia);
                    $numero_righe = count($righe_testo);
                    $pdf->StartTransform();
                    $pdf->Rotate(90);
                    $pdf->MultiCell(70, $dimensione_cella, $testo_materia, 0, 'L', false, 1, '', '', true, 1, false, true, $dimensione_cella, 'M', true);
                    $pdf->SetXY($x_base, $y_base + 3);
                    $pdf->StopTransform();
                    $pdf->SetXY($x_base, $y_base);
                    $pdf->CellFitScale($dimensione_cella, 4, '', 0, 0, 'C');
                    $pdf->SetXY($x_base + $dimensione_cella, $y_base);

                    $testo_excel .= $testo_materia_scelto . "	";
                } elseif ($verticale_orizzontale == "ORIZZ") {
                    $testo_materia = $testo_materia_scelto;
                    $pdf->CellFitScale($dimensione_cella, 4, $testo_materia, 'LTR', 0, 'C');
                    $testo_excel .= $testo_materia_scelto . "	";
                }
                $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;

                if (( ($tipo_visualizzazione_voti == "voto_singolo") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($singola_materia['tipo_voto_personalizzato'] == '1') ) ) && ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")
                ) {
                    $testo_excel .= "	";
                }
                if (( ($tipo_visualizzazione_voti == "scritto_orale") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($singola_materia['tipo_voto_personalizzato'] == '2') ) ) && ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")
                ) {
                    $testo_excel .= "		";
                }
                if (( ($tipo_visualizzazione_voti == "scritto_orale_pratico") || ( ($tipo_visualizzazione_voti == "personalizzato") && ( ($singola_materia['tipo_voto_personalizzato'] == '3') || ($singola_materia['tipo_voto_personalizzato'] == '0') ) ) ) && ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")
                ) {
                    $testo_excel .= "			";
                }
                if (( ($tipo_visualizzazione_voti == "scritto_orale") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($singola_materia['tipo_voto_personalizzato'] == '2') ) ) && ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE")
                ) {
                    $testo_excel .= "	";
                }
                if (( ($tipo_visualizzazione_voti == "scritto_orale_pratico") || ( ($tipo_visualizzazione_voti == "personalizzato") && ( ($singola_materia['tipo_voto_personalizzato'] == '3') || ($singola_materia['tipo_voto_personalizzato'] == '0') ) ) ) && ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE")
                ) {
                    $testo_excel .= "		";
                }
            }
        }
        //}}} </editor-fold>
    }

    $pdf->SetFont('helvetica', 'B', 6);

    if ($visualizza_assenze == "SOLO_ASSENZE") {
        $pdf->CellFitScale($dimensione_cella, 4, "TOT", 'TLR', 0, 'C');
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;
        $testo_excel .= 'TOT	';
    }

    if ($visualizza_assenze == "SOLO_TOTALE") {
        $pdf->CellFitScale($dimensione_cella, 4, "TOT. ASS.", 'TLR', 0, 'C');
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;
        $testo_excel .= 'TOT ASS.	';
    }

    if ($visualizza_assenze == "SI") {
        $pdf->CellFitScale($dimensione_cella, 4, "TOT. ASS.", 'TLR', 0, 'C');
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;
        $testo_excel .= 'TOT ASS.	';
    }

    if ($visualizza_assenze == "PERCENTUALE") {
        $pdf->CellFitScale($dimensione_cella, 4, "TOT. ASS.", 'TLR', 0, 'C');
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;
        $testo_excel .= 'TOT ASS.	';

        // vars x tot.ass.
    }

    if ($stampa_medie == "SI" || $stampa_medie == "SI_TOT") {
        if ($stampa_medie == "SI_TOT") {
            $sum_voti_classe = 0;
            $cont_stud_voti_classe = 0;

            // posizione colonna
            $x_col_media = $pdf->GetX();
        }

        $pdf->CellFitScale($dimensione_cella, 4, "MEDIA", 'TLR', 0, 'C');
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;
        $testo_excel .= 'MEDIA	';
    }
    //Fra 04/01/13 creata instestazione tabella per la scuola media di provenienza
    if ($stampa_medie_provenienza == "SI") {
        $pdf->CellFitScale($dimensione_cella * 3, 4, "SCUOLA MEDIA", 'TLR', 0, 'C');
        $testo_excel .= 'SCUOLA MEDIA	';
    }

    if ($stampa_crediti == "SI") {
        $pdf->CellFitScale($dimensione_cella * 2, 4, "CREDITI", 'TLR', 0, 'C');
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella;
        $testo_excel .= 'CREDITI	';
    }

    if ($stampa_crediti_anno_corrente == "SI") {
        $x_base = $pdf->GetX();
        $y_base = $pdf->GetY();
        $pdf->SetXY($x_base, $y_base);
        $pdf->CellFitScale($dimensione_cella / 2, 1, "CREDITI", 'TLR', 0, 'C');
        $pdf->SetXY($x_base, $y_base + 2);
        $pdf->CellFitScale($dimensione_cella / 2, 2, "ANNO", 'LR', 0, 'C');
        $pdf->SetXY($x_base, $y_base + 2);
        $pdf->CellFitScale($dimensione_cella / 2, 6, "CORRENTE", 'BLR', 0, 'C');
        $pdf->SetXY($x_base + $dimensione_cella / 2, $y_base);
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella / 2;
        $testo_excel .= 'CREDITI	';
    }

    if ($stampa_crediti_integrativi != "NO") {
        $x_base = $pdf->GetX();
        $y_base = $pdf->GetY();
        $pdf->SetXY($x_base, $y_base);
        $pdf->CellFitScale($dimensione_cella / 2, 1, "CREDITI", 'TLR', 0, 'C');
        $pdf->SetXY($x_base, $y_base + 2);
        $pdf->CellFitScale($dimensione_cella / 2, 2, "INTEGR", 'LR', 0, 'C');

        $testo_crediti = $stampa_crediti_integrativi == 'TOT' ? "TOTALI" : "A.C.";
        $pdf->SetXY($x_base, $y_base + 2);
        $pdf->CellFitScale($dimensione_cella / 2, 6, $testo_crediti, 'BLR', 0, 'C');

        $pdf->SetXY($x_base + $dimensione_cella / 2, $y_base);
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella / 2;
        $testo_excel .= 'CREDITI	';
    }

    if ($stampa_crediti_totali == "SI") {
        $x_base = $pdf->GetX();
        $y_base = $pdf->GetY();
        $pdf->SetXY($x_base, $y_base);
        $pdf->CellFitScale($dimensione_cella / 2, 4, "TOT", 'TLR', 0, 'C');
        $pdf->SetXY($x_base, $y_base + 4);
        $pdf->CellFitScale($dimensione_cella / 2, 2, "CR.", 'BLR', 0, 'C');
        $pdf->SetXY($x_base + $dimensione_cella / 2, $y_base);
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella / 2;
        $testo_excel .= 'TOT	';
    }

    //    if($stampa_crediti_totali_om_2022 == "SI")
    //    {
    //        $x_base = $pdf->GetX();
    //        $y_base = $pdf->GetY();
    //        $pdf->SetXY($x_base,$y_base);
    //        $pdf->CellFitScale($dimensione_cella/2,4,"CR",'TLR',0,'C');
    //        $pdf->SetXY($x_base,$y_base + 4);
    //        $pdf->CellFitScale($dimensione_cella/2,2,"CONV",'BLR',0,'C');
    //        $pdf->SetXY($x_base + $dimensione_cella/2,$y_base);
    //        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella/2;
    //        $testo_excel .= 'TOT OM 65    ';
    //    }

    if ($stampa_risultato != "NO") {
        if ($stampa_risultato == "SI_CON_VALORI" && $dati_classe['tipo_indirizzo'] == 4 && $dati_classe['classe'] == 3
        ) {
            if ($stringa_anno_scolastico == '2019/2020') {
                $pdf->CellFitScale($dimensione_cella * 3, 4, "RISULTATO", 'TLR', 0, 'C');
            } else {
                $pdf->CellFitScale($dimensione_cella * 2.3, 4, "RISULTATO", 'TLR', 0, 'C');
                $pdf->CellFitScale($dimensione_cella * 0.7, 4, "AMM.", 'TLR', 0, 'C');
            }
            $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella * 3;
            $testo_excel .= 'RISULTATO	';
            $testo_excel .= 'VOTO AMMISSIONE	';
        } else {
            $pdf->CellFitScale($dimensione_cella * 3, 4, "RISULTATO", 'TLR', 0, 'C');
            $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella * 3;
            $testo_excel .= 'RISULTATO	';
        }
    }

    if ($stampa_annotazioni == "SI") {
        $pdf->CellFitScale($dimensione_cella * 2, 4, "ANNOTAZIONI", 'TLR', 0, 'C');
        $tot_larghezza_usata = $tot_larghezza_usata + $dimensione_cella * 2;
        $testo_excel .= 'ANNOTAZIONI	';
    }


    //$trentino_abilitato definito in index.php
    if ($stampa_debiti_4a == "SI") {
        if ($trentino_abilitato == 'SI') {
            $pdf->CellFitScale($dimensione_cella * 3, 4, "CARENZE", 'TLR', 0, 'C');
            $testo_excel .= 'CARENZE	';
        } else {
            $pdf->CellFitScale($dimensione_cella * 3, 4, "MATERIE CON RECUPERO", 'TLR', 0, 'C');
            $testo_excel .= 'MATERIE CON RECUPERO	';
        }
    }

    if ($stampa_debiti_4b == "SI") {
        if ($trentino_abilitato == 'SI') {
            $pdf->CellFitScale($dimensione_cella * 3, 4, "CAR. RECUPERO AUTON.", 'TLR', 0, 'C');
            $testo_excel .= 'DEB. RECUPERO AUTON.	';
        } else {
            $pdf->CellFitScale($dimensione_cella * 3, 4, "MATERIE CON RECUPERO AUTON.", 'TLR', 0, 'C');
            $testo_excel .= 'MATERIE CON RECUPERO AUTON.	';
        }
    }


    $pdf->Cell(0, 4, '', 0, 1, 'C');
    $testo_excel .= chr(13) . chr(10);

    $pdf->SetFont('helvetica', 'B', 5);
    $pdf->Cell(5, 2, '', 'LBR', 0, 'R');
    $pdf->Cell(30, 2, '', 'LBR', 0, 'L');
    $testo_excel .= "		";

    $margine_cella = $pdf->cMargin;

    if ($stampa_tutti_voti == "SI") {
        //{{{ <editor-fold defaultstate="collapsed" desc="dettaglio del tipo di voto e delle assenze">
        foreach ($elenco_materie_classe as $cont => $singola_materia) {
            if (( ($tipo_visualizzazione_voti == "voto_singolo") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '1') ) ) && ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")
            ) {
                if (($periodo != '11') || ( ($periodo == '11') && ($elenco_materie_classe[$cont]['in_media_pagelle'] == 'SI') )
                ) {
                    if ($elenco_materie_classe[$cont]['tipo_materia'] != "CONDOTTA") {
                        if ($parametro_debiti == "SI") {
                            $pdf->Cell(($dimensione_cella / 3), 2, 'Voto', 'LB', 0, 'C');
                            $pdf->Cell(($dimensione_cella / 3), 2, 'DF', 'B', 0, 'C');
                            $pdf->Cell(($dimensione_cella / 3), 2, 'Ass.', 'BR', 0, 'C');
                            $testo_excel .= "Voto	DF    Ass.	";
                        } else {
                            $pdf->Cell($dimensione_mezza_cella, 2, 'Voto', 'LB', 0, 'C');
                            $pdf->Cell($dimensione_mezza_cella, 2, 'Ass.', 'BR', 0, 'C');
                            $testo_excel .= "Voto	Ass.	";
                        }
                    } else {
                        if ($parametro_debiti == "SI") {
                            $pdf->Cell($dimensione_mezza_cella, 2, 'Voto', 'LB', 0, 'C');
                            $pdf->Cell($dimensione_mezza_cella, 2, 'DF', 'BR', 0, 'C');
                            $testo_excel .= "Voto	DF    ";
                        } else {
                            $pdf->Cell($dimensione_cella, 2, 'Voto', 'LBR', 0, 'C');
                            $testo_excel .= "Voto	";
                        }
                    }
                }
            }

            if (( ($tipo_visualizzazione_voti == "voto_singolo") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '1') ) ) && ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE")
            ) {
                if (($periodo != '11') || ( ($periodo == '11') && ($elenco_materie_classe[$cont]['in_media_pagelle'] == 'SI') )
                ) {
                    if ($parametro_debiti == "SI") {
                        $pdf->Cell($dimensione_mezza_cella, 2, 'Voto', 'LB', 0, 'C');
                        $pdf->Cell($dimensione_mezza_cella, 2, 'DF', 'BR', 0, 'C');
                        $testo_excel .= "Voto	DF    ";
                    } else {
                        $pdf->Cell($dimensione_cella, 2, 'Voto', 'LBR', 0, 'C');
                        $testo_excel .= "Voto	";
                    }
                }
            }


            $pdf->cMargin = $valore_margine_cella_voti;
            if (( ($tipo_visualizzazione_voti == "scritto_orale_pratico") || ( ($tipo_visualizzazione_voti == "personalizzato") && ( ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '3') || ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '0') ) ) ) && ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")
            ) {
                if (($periodo != '11') || ( ($periodo == '11') && ($elenco_materie_classe[$cont]['in_media_pagelle'] == 'SI') )
                ) {
                    if ($elenco_materie_classe[$cont]['tipo_materia'] != "CONDOTTA") {
                        if ($parametro_debiti == "SI") {
                            $pdf->CellFitScale($dimensione_cella / 5, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 5, 2, 'OR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 5, 2, 'PR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 5, 2, 'DF', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 5, 2, 'As.', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR  PR	DF    Ass.	";
                        } else {
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'OR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'PR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'As.', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR	PR	Ass.	";
                        }
                    } else {
                        $pdf->CellFitScale($dimensione_cella, 2, 'Voto', 'LBR', 0, 'C');
                        $testo_excel .= "Voto	";
                    }
                }
            }


            if (( ($tipo_visualizzazione_voti == "scritto_orale_pratico") || ( ($tipo_visualizzazione_voti == "personalizzato") && ( ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '3') || ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '0') ) ) ) && ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE")
            ) {
                if (($periodo != '11') || ( ($periodo == '11') && ($elenco_materie_classe[$cont]['in_media_pagelle'] == 'SI') )
                ) {
                    if ($elenco_materie_classe[$cont]['tipo_materia'] != "CONDOTTA") {
                        if ($parametro_debiti == "SI") {
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'OR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'PR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'DF', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR  PR	DF    ";
                        } else {
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'OR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'PR', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR	PR	";
                        }
                    } else {
                        $pdf->CellFitScale($dimensione_cella, 2, 'Voto', 'LBR', 0, 'C');
                        $testo_excel .= "S/G	OR	PR	";
                    }
                }
            }

            if (( ($tipo_visualizzazione_voti == "scritto_orale") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '2') ) ) && ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")
            ) {
                if (($periodo != '11') || ( ($periodo == '11') && ($elenco_materie_classe[$cont]['in_media_pagelle'] == 'SI') )
                ) {
                    if ($elenco_materie_classe[$cont]['tipo_materia'] != "CONDOTTA") {
                        if ($parametro_debiti == "SI") {
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'OR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'DF', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 4, 2, 'As.', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR  DF    Ass.    ";
                        } else {
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'OR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'As.', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR	Ass.	";
                        }
                    } else {
                        $pdf->CellFitScale($dimensione_cella, 2, 'Voto', 'LBR', 0, 'C');
                        $testo_excel .= "Voto	";
                    }
                }
            }

            if (( ($tipo_visualizzazione_voti == "scritto_orale") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($elenco_materie_classe[$cont]['tipo_voto_personalizzato'] == '2') ) ) && ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE")
            ) {
                if (($periodo != '11') || ( ($periodo == '11') && ($elenco_materie_classe[$cont]['in_media_pagelle'] == 'SI') )
                ) {
                    if ($elenco_materie_classe[$cont]['tipo_materia'] != "CONDOTTA") {
                        if ($parametro_debiti == "SI") {
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'OR', 'B', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 3, 2, 'DF', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR  DF    ";
                        } else {
                            $pdf->CellFitScale($dimensione_cella / 2, 2, 'S/G', 'LB', 0, 'C');
                            $pdf->CellFitScale($dimensione_cella / 2, 2, 'OR', 'BR', 0, 'C');
                            $testo_excel .= "S/G	OR	";
                        }
                    } else {
                        $pdf->CellFitScale($dimensione_cella, 2, 'Voto', 'LBR', 0, 'C');
                        $testo_excel .= "Voto	";
                    }
                }
            }

            if ($visualizza_assenze == "SOLO_ASSENZE") {
                if (($periodo != '11') || ( ($periodo == '11') && ($elenco_materie_classe[$cont]['in_media_pagelle'] == 'SI') )
                ) {
                    $pdf->CellFitScale($dimensione_cella, 2, 'ORE', 'LBR', 0, 'C');
                    $testo_excel .= "ORE	";
                }
            }
        }
        //$pdf->Cell($dimensione_cella,2,'','LRB',0,'C');
        $pdf->cMargin = $margine_cella;
        //}}} </editor-fold>
    } elseif ($stampa_tutti_voti == 'SOLO_RECUPERI') {
        foreach ($elenco_materie_classe as $cont => $singola_materia) {
            $pdf->CellFitScale($dimensione_cella, 2, '', 'LBR', 0, 'C');
            $testo_excel .= "   	  	  	";
        }
        $pdf->cMargin = $margine_cella;
    }

    if ($visualizza_assenze != 'NO' && $visualizza_assenze != 'SENZA_TOTALE') {
        $pdf->CellFitScale($dimensione_cella, 2, '', 'LBR', 0, 'C');
        $testo_excel .= "	";
    }

    if ($stampa_medie == "SI" || $stampa_medie == "SI_TOT") {
        $pdf->Cell($dimensione_cella, 2, '', 'LRB', 0, 'C');
    }

    if ($stampa_medie_provenienza == "SI") {
        $pdf->Cell($dimensione_cella * 3, 2, '', 'LRB', 0, 'C');
    }

    if ($stampa_crediti == "SI") {
        //echo_debug($dati_classe);
        if ($dati_classe['tipo_indirizzo'] == '5') {
            $pdf->Cell($dimensione_cella * 2 / 3, 2, '2°', 'TLRB', 0, 'C');
            $pdf->Cell($dimensione_cella * 2 / 3, 2, '3°', 'TLRB', 0, 'C');
            $pdf->Cell($dimensione_cella * 2 / 3, 2, '4°', 'TLRB', 0, 'C');
        } else {
            $pdf->Cell($dimensione_cella * 2 / 3, 2, '3°', 'TLRB', 0, 'C');
            $pdf->Cell($dimensione_cella * 2 / 3, 2, '4°', 'TLRB', 0, 'C');
            $pdf->Cell($dimensione_cella * 2 / 3, 2, '5°', 'TLRB', 0, 'C');
        }
    }

    if ($stampa_crediti_anno_corrente == "SI") {
        $pdf->Cell($dimensione_cella / 2, 2, '', 'LRB', 0, 'C');
    }

    if ($stampa_crediti_integrativi != "NO") {
        $pdf->Cell($dimensione_cella / 2, 2, '', 'LRB', 0, 'C');
    }

    if ($stampa_crediti_totali == "SI") {
        $pdf->Cell($dimensione_cella / 2, 2, '', 'LRB', 0, 'C');
    }

    //    if($stampa_crediti_totali_om_2022 == "SI")
    //    {
    //        $pdf->Cell($dimensione_cella/2,2,'','LRB',0,'C');
    //    }

    if (($stampa_risultato == "SI_CON_VALORI") || ($stampa_risultato == "SI_SENZA_VALORI")) {
        if ($stampa_risultato == "SI_CON_VALORI" && $dati_classe['tipo_indirizzo'] == 4 && $dati_classe['classe'] == 3
        ) {
            if ($stringa_anno_scolastico == '2019/2020') {
                $pdf->CellFitScale($dimensione_cella * 3, 2, "", 'LR', 0, 'C');
            } else {
                $pdf->Cell($dimensione_cella * 2.3, 2, '', 'LRB', 0, 'C');
                $pdf->Cell($dimensione_cella * 0.7, 2, '', 'LRB', 0, 'C');
            }
        } else {
            $pdf->Cell($dimensione_cella * 3, 2, '', 'LRB', 0, 'C');
        }
    }

    if ($stampa_annotazioni == "SI") {
        $pdf->CellFitScale($dimensione_cella * 2, 2, "", 'LRB', 0, 'C');
    }


    if ($stampa_debiti_4a == "SI") {
        $pdf->Cell($dimensione_cella * 3, 2, '', 'LRB', 0, 'C');
    }

    if ($stampa_debiti_4b == "SI") {
        $pdf->Cell($dimensione_cella * 3, 2, '', 'LRB', 0, 'C');
    }


    $pdf->Cell(0, 2, '', 0, 1, 'C');
    $testo_excel .= chr(13) . chr(10);

    $cont_tot_comma4a = 0;
    $cont_tot_comma4b = 0;

    $pdf->SetFillColor(224, 235, 255);
    //}}} </editor-fold>

    $altezza_std = $altezza_cella;

    for ($cont_stud = 0; $cont_stud < count($elenco_studenti_classe); $cont_stud++) {
        //{{{ <editor-fold defaultstate="collapsed" desc="Ciclo studenti">
        $altezza_cella = $altezza_std;

        $pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);
        $id_studente = $elenco_studenti_classe[$cont_stud][0];

        $altezza_nomi = $pdf->MultiCellNbLines(30, $elenco_studenti_classe[$cont_stud][2] . ' ' . $elenco_studenti_classe[$cont_stud][1]) * $altezza_cella;
        //print $altezza_cella . '    ' . $altezza_nomi . '<br>';
        if ($altezza_nomi > $altezza_cella) {
            //$altezza_cella = $altezza_nomi;
            $multi_cell = 'NO';
        }

        //Fra 04/01/13 compongo le informazioni che mi servono della scuola di provenienza in un'unica variabile
        //per adesso uso il nome completo e il codice meccanografico
        $scuola_media_provenienza = $elenco_studenti_classe[$cont_stud]['mat_esito']['nome_scuola_media_provenienza'] . ' - ' . $elenco_studenti_classe[$cont_stud]['mat_esito']['scuola_media_provenienza'];
        //Fra 04/01/13 se l'anno totale del curriculum corrisponde ad una prima superiore abilito la stampa
        //Quindi quando non viene stampata la scuola di uno studente controllare il curriculum
        if ($elenco_studenti_classe[$cont_stud]['anno_totale_classe'] == '9' || $elenco_studenti_classe[$cont_stud]['anno_totale_classe'] == '29') {
            $abilita_stampa_medie_provenienza = 'SI';
        }

        $elenco_voti_pagelline = $elenco_voti_pagelline_totale[$id_studente];

        if ($id_studente >= 0) {
            $dati_studente = estrai_dati_studente_completi((int) $id_studente);
            if ($dati_studente['sesso'] == 'F') {
                $suffisso_oa = 'a';
                $suffisso_OA = 'A';
            } else {
                $suffisso_oa = 'o';
                $suffisso_OA = 'O';
            }

            $dati_debiti = estrai_debiti_studente((int) $id_studente, $anno_scolastico);

            $freq_estero = 'NO';
            $curriculum_studente = estrai_curriculum_studente($id_studente);
            for ($cont_curr = 0; $cont_curr < count($curriculum_studente); $cont_curr++) {
                if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico && $curriculum_studente[$cont_curr]['esito'] == 'Frequenza anno estero'
                ) {
                    $freq_estero = 'SI';
                }
            }

            if ($dati_studente['data_ritiro'] > 0 && $dati_studente['data_ritiro'] < mktime(0, 0, 0, 3, 16, $anno_fine) && (stripos($dati_studente['esito'], 'ritirato') !== false || stripos($dati_studente['esito'], 'trasferito') !== false) && $visualizza_ritirati == "NO"
            ) {
                if ($celle_colorate == 'SI') {
                    $pdf->SetFillColor(255, 255, 255);
                    $fill = 1;
                } else {
                    $fill = !$fill;
                }
            } else {
                //{{{ <editor-fold defaultstate="collapsed" desc="Studente in corso">
                if ($dati_studente['pei'] == 'SI' && $attiva_stampa_pei == 'SI' && $dati_studente['tipo_handicap'] != 'DSA') {
                    $stampa_pei = 'SI';
                    $dsa = false;
                } elseif ($dati_studente['pei'] == 'SI' && $attiva_stampa_pei == 'SI' && $dati_studente['tipo_handicap'] == 'DSA') {
                    $stampa_pei = 'SI';
                    $dsa = true;
                }

                if ($dati_studente[26] == "1") {
                    $dati_edfisica = estrai_id_materia_esonerata("E");
                    $id_edfisica = $dati_edfisica[0];
                }

                $tot_ore_assenze = 0;
                $tot_monteore_assenze = 0;
                $media_voti_pagella = 0;
                $somma_voti_pagella = 0;
                $tot_voti_pagella = 0;


                $esistenza_recuperi = 'NO';

                if ($periodo == 9 || $periodo == 29) {
                    $esito = calcola_esito_finale_studente((int) $id_studente, (int) $current_user, true);
                } else {
                    $esito = calcola_esito_pagella((int) $id_studente, $periodo, (int) $current_user);
                }

                $controllo_voto_singolo = decode($esito['esito']);
                // CONVERSIONE IN BASE AL SESSO AMMESSO/A
                $controllo_voto_singolo = str_replace('messo', "mess$suffisso_oa", $controllo_voto_singolo);
                $controllo_voto_singolo = str_replace('MESSO', "MESS$suffisso_OA", $controllo_voto_singolo);

                //                if($controllo_voto_singolo == 'Ammesso esame di stato' && $dati_studente['pei'] == 'SI' && $dati_classe["tipo_indirizzo"] != 4)
                //                {
                //                    $controllo_voto_singolo = "ATTESTAZIONE ai sensi dell'art. 13, D.P.R 323/1998";
                //                }
                //{{{ <editor-fold defaultstate="collapsed" desc="Debiti">
                $elenco_debiti_comma_4a = $esito['debiti_comma_4a'];
                $cont_debiti_comma4a = $esito['cont_debiti_comma4a'];
                $elenco_debiti_comma_4b = $esito['debiti_comma_4b'];
                $cont_debiti_comma4b = $esito['cont_debiti_comma4b'];

                if ($anno_inizio >= '2007' && $trentino_abilitato != 'SI') {
                    //{{{ <editor-fold defaultstate="collapsed">
                    $stringa_debiti_4a = '';

                    if (is_array($elenco_debiti_comma_4a)) {
                        foreach ($elenco_debiti_comma_4a as $singolo_debito) {
                            //                            $stringa_debiti_4a .= $singolo_debito . chr(13) . chr(10);
                            $stringa_debiti_4a .= $singolo_debito . ', ';
                        }
                    }

                    $stringa_debiti_4b = '';

                    if (is_array($elenco_debiti_comma_4b)) {
                        foreach ($elenco_debiti_comma_4b as $singolo_debito) {
                            $stringa_debiti_4b .= $singolo_debito . chr(13) . chr(10);
                        }
                    }
                    //}}} </editor-fold>
                } else {
                    //{{{ <editor-fold defaultstate="collapsed">
                    // Se sei una scuola del Trentino
                    // Fra, 22/01/2018 : da rivedere a modo. Ho messo questo controllo (ovvero se siamo nel 1 quad) perchè il filzi vuole che
                    // vengano stampati i recuperi nel 1 quad invece che le carenze
                    // per adesso soluzione accettabile, se altre scuole non lo vogliono inserire parametro
                    if ($periodo == '7') {
                        $stringa_debiti_4a = '';

                        if (is_array($elenco_debiti_comma_4a)) {
                            foreach ($elenco_debiti_comma_4a as $singolo_debito) {
                                //                                $stringa_debiti_4a .= $singolo_debito . chr(13) . chr(10);
                                $stringa_debiti_4a .= $singolo_debito . ', ';
                            }
                        }

                        $stringa_debiti_4b = '';

                        if (is_array($elenco_debiti_comma_4b)) {
                            foreach ($elenco_debiti_comma_4b as $singolo_debito) {
                                $stringa_debiti_4b .= $singolo_debito . chr(13) . chr(10);
                            }
                        }
                    } else {
                        $elenco_debiti_comma_4a = [];
                        $elenco_debiti_comma_4b = [];
                        $cont_debiti_comma4a = 0;
                        $cont_debiti_comma4b = 0;
                        $stringa_debiti_4a = '';
                        $stringa_debiti_4b = '';

                        if (is_array($dati_debiti)) {
                            foreach ($dati_debiti as $debito) {
                                if ($debito["tipo_debito"] == "CON VERIFICA") {
                                    //                                    $stringa_debiti_4a .= $debito["descrizione_materia"] . chr(13) . chr(10);
                                    $stringa_debiti_4a .= $debito["descrizione_materia"] . ', ';
                                    $elenco_debiti_comma_4a[$cont_debiti_comma4a] = $debito["descrizione_materia"];
                                    $cont_debiti_comma4a++;
                                    $cont_tot_comma4a++;
                                } else {
                                    $stringa_debiti_4b .= $debito["descrizione_materia"] . chr(13) . chr(10);
                                    $elenco_debiti_comma_4b[$cont_debiti_comma4b] = $debito["descrizione_materia"];
                                    $cont_debiti_comma4b++;
                                    $cont_tot_comma4b++;
                                }
                            }
                        }

                        if (is_array($dati_debiti) && $stampo_debiti_sui_voti == 'SI') {
                            $controllo_voto_singolo = decode($esito['esito']) . " *";
                            // CONVERSIONE IN BASE AL SESSO AMMESSO/A
                            $controllo_voto_singolo = str_replace('messo', "mess$suffisso_oa", $controllo_voto_singolo);
                            $controllo_voto_singolo = str_replace('MESSO', "MESS$suffisso_OA", $controllo_voto_singolo);
                        }
                    }
                    //}}} </editor-fold>
                }
                //}}} </editor-fold>
                //{{{ <editor-fold defaultstate="collapsed" desc="Calcolo se stampare o no lo studente">
                if ($esito['recupero']) {
                    $esistenza_recuperi = 'SI';
                } else {
                    $esistenza_recuperi = 'NO';
                }

                if ($esito['positivo'] === true) {
                    $passato = "SI";
                } elseif ($esito['positivo'] === false) {
                    $passato = "NO";
                } else {
                    $passato = "NI";
                }

                if ($stampa_tutti_studenti == 'SI') {
                    $stampa_singolo_stud = 'SI';
                } elseif ($stampa_tutti_studenti == 'NE') {
                    if ($esistenza_recuperi == 'SI' || $freq_estero == 'SI') {
                        $stampa_singolo_stud = 'SI';
                    } else {
                        $stampa_singolo_stud = 'NO';
                    }

                    if ($controllo_voto_singolo == 'Frequenza anno estero') {
                        $da_estero = 'SI';
                    } else {
                        $da_estero = 'NO';
                    }
                } else {
                    if ($esistenza_recuperi == 'SI') {
                        $stampa_singolo_stud = 'SI';
                    } else {
                        $stampa_singolo_stud = 'NO';
                    }
                }

                if ($visualizza_studenti_senza_voti == 'NO' && ($periodo != 9 && $periodo != 29)) {
                    $trovato_voto = false;
                    foreach ($elenco_voti_pagelline as $voto_pagellina) {
                        if ($voto_pagellina['in_media_pagelle'] == 'SI' && ( ( ( $tipo_visualizzazione_voti == "voto_singolo" || ( $tipo_visualizzazione_voti == "personalizzato" && $voto_pagellina['tipo_voto_personalizzato'] == '1' ) ) && $voto_pagellina['voto_pagellina'] != '' ) || ( ( $tipo_visualizzazione_voti != "voto_singolo" || ( $tipo_visualizzazione_voti == "personalizzato" && $voto_pagellina['tipo_voto_personalizzato'] == '2' ) ) && ( $voto_pagellina['voto_scritto_pagella'] != '' || $voto_pagellina['voto_orale_pagella'] != '' ) ) || ( ( $tipo_visualizzazione_voti != "voto_singolo" || ( $tipo_visualizzazione_voti == "personalizzato" && $voto_pagellina['tipo_voto_personalizzato'] == '2' ) ) && ( $voto_pagellina['voto_scritto_pagella'] != '' || $voto_pagellina['voto_orale_pagella'] != '' || $voto_pagellina['voto_pratico_pagella'] != '' ) ) )
                        ) {
                            $trovato_voto = true;
                            break;
                        }
                    }

                    if (!$trovato_voto) {
                        $stampa_singolo_stud = 'NO';
                    }
                }
                //}}} </editor-fold>

                if ($stampa_singolo_stud == 'SI') {
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($celle_colorate == 'SI') {
                        $pdf->SetFillColor(255, 255, 255);
                        $fill = 1;
                    } else {
                        $fill = !$fill;
                    }

                    //{{{ <editor-fold defaultstate="collapsed" desc="calcolo come stampare il nome dello studente">
                    if ($visualizza_nomi_bocciati == "NO" && $passato == "NO") {
                        $pdf->CellFitScale(5, $altezza_cella, '', 1, 0, 'R', $fill);
                        $testo_excel .= "	";
                        $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'L', $fill);
                        $testo_excel .= "	";
                    } elseif ($visualizza_nomi_sospesi == "NO" && $passato == "NI") {
                        $pdf->CellFitScale(5, $altezza_cella, '', 1, 0, 'R', $fill);
                        $testo_excel .= "	";
                        $pdf->CellFitScale(30, $altezza_cella, '', 1, 0, 'L', $fill);
                        $testo_excel .= "	";
                    } else {
                        if ($dati_studente['pei'] == 'SI' && $attiva_stampa_pei == 'SI' && !$dsa) {
                            $cognome = "* " . $dati_studente['cognome'];
                        } elseif ($dati_studente['pei'] == 'SI' && $attiva_stampa_pei == 'SI' && $dsa) {
                            $cognome = "** " . $dati_studente['cognome'];
                        } else {
                            $cognome = $dati_studente['cognome'];
                        }

                        if ($multi_cell == 'SI') {
                            $pdf->MultiCell(5, $altezza_cella, $elenco_studenti_classe[$cont_stud][3], 1, 'R', $fill, 0, '', '', true, 0, false, true, $altezza_cella, 'M', true);
                            $pdf->MultiCell(30, $altezza_cella, $cognome . ' ' . $elenco_studenti_classe[$cont_stud][1], 1, 'L', $fill, 0, '', '', true, 0, false, true, $altezza_cella, 'M', true);
                        } else {
                            $pdf->CellFitScale(5, $altezza_cella, $elenco_studenti_classe[$cont_stud][3], 1, 0, 'R', $fill);
                            $pdf->CellFitScale(30, $altezza_cella, $cognome . ' ' . $elenco_studenti_classe[$cont_stud][1], 1, 0, 'L', $fill);
                        }
                        $testo_excel .= $elenco_studenti_classe[$cont_stud][3] . "	";
                        $testo_excel .= $cognome . ' ' . $elenco_studenti_classe[$cont_stud][1] . "	";
                    }
                    //}}} </editor-fold>

                    $pdf->SetFont('helvetica', 'B', $dimensione_font);

                    $elenco_debiti = $dati_debiti;

                    $tot_insuff = 0; //echo_debug($elenco_voti_pagelline);
                    foreach ($elenco_voti_pagelline as $voto_pagellina) {
                        if ($voto_pagellina['voto_pagellina'] > $voto_minimo && $voto_pagellina['voto_pagellina'] < $voto_minimo_suff && $voto_pagellina['in_media_pagelle'] == "SI"
                        ) {
                            $tot_insuff++;
                        }
                    }
                    $tot_ore_assenze_colonna_unica = 0;
                    $tot_ore_assenze_colonna_unica_arrotondate = 0;
					foreach ($elenco_materie_classe as $chiave => $singola_materia) {
                        $voto_unico = false;
						//{{{ <editor-fold defaultstate="collapsed" desc="ciclo voti">
						$cont = $singola_materia['id_materia'];
                        $tot_ore_assenze_colonna_unica = $tot_ore_assenze_colonna_unica + $elenco_voti_pagelline[$cont][12];
						if ($elenco_voti_pagelline[$cont][12] == "") {
							$elenco_voti_pagelline[$cont][12] = "0";
						}

						//{{{ <editor-fold defaultstate="collapsed" desc="commento al non voto, somme per calcolo media e asterischi debiti">
						//controllo sul codice di significati_voto
                        if ($elenco_voti_pagelline[$cont][10] == "" || $elenco_voti_pagelline[$cont][10] == "-1") {
							$elenco_voti_pagelline[$cont][10] = $commento_al_non_voto;
						} elseif($elenco_voti_pagelline[$cont][10] == "NC") {
                            $elenco_voti_pagelline[$cont][10] = "NC";
                        }

                        //{{{ <editor-fold defaultstate="collapsed" desc="commento al non voto, somme per calcolo media e asterischi debiti">
                        //controllo sul codice di significati_voto
                        if ($elenco_voti_pagelline[$cont][10] == "" || $elenco_voti_pagelline[$cont][10] == "-1") {
                            $elenco_voti_pagelline[$cont][10] = $commento_al_non_voto;
                        } elseif ($elenco_voti_pagelline[$cont][10] == "NC") {
                            $elenco_voti_pagelline[$cont][10] = "NC";
                        } else {
                            //{{{ <editor-fold defaultstate="collapsed" desc="somme per calcolo media">
                            if ($elenco_voti_pagelline[$cont]['in_media_pagelle'] == 'SI' && ( $tipo_visualizzazione_voti == "voto_singolo" || ( $tipo_visualizzazione_voti == "personalizzato" && $singola_materia['tipo_voto_personalizzato'] == '1' ) )
                            ) {
                                // voto_unico_pagellina
                                $voto_per_media = 0;
                                $voto_unico = true;
                                if ($elenco_voti_pagelline[$cont][6] != 'NC' && $elenco_voti_pagelline[$cont][6] != 'A') {
                                    $voto_per_media = $elenco_voti_pagelline[$cont][6];
                                    $tot_voti_pagella = $tot_voti_pagella + 1;
                                }
                                // Fra: 10/06/2017 Messo 6 così da aggiornare anche la medie col voto sostituito
                                if ($trentino_abilitato == 'SI' && $esposizione_archivio == 'esposizione' && in_array(intval($elenco_voti_pagelline[$cont][10]), [1, 2, 3, 4, 5]) && verifica_presenza_debito($id_studente, $elenco_voti_pagelline[$cont]['id_materia'], $anno_scolastico)
                                ) {
                                    $elenco_voti_pagelline[$cont][6] = 6;
                                    $voto_per_media = 6;
                                } elseif ($trentino_abilitato == 'SI' && $esposizione_archivio == 'esposizione' && ($elenco_voti_pagelline[$cont][10] == '') && ($dati_studente['pei'] == 'SI' || $dati_studente['bes'] == 'SI')
                                ) {
                                    $elenco_voti_pagelline[$cont][6] = 6;
                                    $voto_per_media = 6;
                                }

                                // Media
                                // Se siamo in Trentino, ci sono debiti ed il voto è insufficiente
                                if ($trentino_abilitato == 'SI' && verifica_presenza_debito($id_studente, $elenco_voti_pagelline[$cont]['id_materia'], $anno_scolastico) && in_array(intval($elenco_voti_pagelline[$cont][10]), [1, 2, 3, 4, 5])
                                ) {
                                    $voto_per_media = 6;
                                }

                                //$somma_voti_pagella = $somma_voti_pagella + $elenco_voti_pagelline[$cont][6];
                                $somma_voti_pagella += $voto_per_media;
                            }

                            //}}} </editor-fold>
                            //{{{ <editor-fold defaultstate="collapsed" desc="asterischi debiti">
                            //                            if($elenco_voti_pagelline[$cont][7] == "D" && $stampo_debiti_sui_voti != "NO")
                            //                            {
                            //
                            //                                $elenco_voti_pagelline[$cont][10] = "*" . $elenco_voti_pagelline[$cont][10];
                            //                            }
                            //                            else
                            //                            {
                            //                                if($elenco_voti_pagelline[$cont][7] == "A" && $stampo_debiti_sui_voti != "NO")
                            //                                {
                            //                                    $elenco_voti_pagelline[$cont][10] = "**" . $elenco_voti_pagelline[$cont][10];
                            //                                }
                            //                            }
                            //}}} </editor-fold>
                        }

                        if (!$voto_unico) {
                            //controllo sul codice di significati_voto
                            if ($elenco_voti_pagelline[$cont][60] == "" || $elenco_voti_pagelline[$cont][60] == "-1") {
                                $elenco_voti_pagelline[$cont][60] = $commento_al_non_voto;
                            } else {
                                //{{{ <editor-fold defaultstate="collapsed" desc="somme per calcolo media">
                                if ($elenco_voti_pagelline[$cont]['in_media_pagelle'] == 'SI' && ( $tipo_visualizzazione_voti != "voto_singolo" || ( $tipo_visualizzazione_voti == "personalizzato" && ($singola_materia['tipo_voto_personalizzato'] == '2' || $singola_materia['tipo_voto_personalizzato'] == '3') ) )
                                ) {
                                    /*    ($tipo_visualizzazione_voti != "voto_singolo")
                                    ||
                                    (
                                    ($tipo_visualizzazione_voti == "personalizzato")
                                    &&
                                    ($singola_materia['tipo_voto_personalizzato'] != '1')
                                    )
                                    ) */
                                    // voto_scritto_pagellina
                                    $somma_voti_pagella = $somma_voti_pagella + $elenco_voti_pagelline[$cont][50];
                                    $tot_voti_pagella = $tot_voti_pagella + 1;
                                }
                                //}}} </editor-fold>
                                //{{{ <editor-fold defaultstate="collapsed" desc="asterischi debiti">
                                if ($elenco_voti_pagelline[$cont][7] == "D") {
                                    $elenco_voti_pagelline[$cont][50] = "*" . $elenco_voti_pagelline[$cont][50];
                                }
                                if ($elenco_voti_pagelline[$cont][7] == "A") {
                                    $elenco_voti_pagelline[$cont][50] = "**" . $elenco_voti_pagelline[$cont][50];
                                }
                                //}}} </editor-fold>
                            }

                            //controllo sul codice di significati_voto
                            if (($elenco_voti_pagelline[$cont][61] == "") || ($elenco_voti_pagelline[$cont][61] == "-1")) {
                                $elenco_voti_pagelline[$cont][61] = $commento_al_non_voto;
                            } else {
                                //{{{ <editor-fold defaultstate="collapsed" desc="somme per calcolo media">
                                if ($elenco_voti_pagelline[$cont]['in_media_pagelle'] == 'SI') {
                                    if ($tipo_visualizzazione_voti != "voto_singolo" || ( $tipo_visualizzazione_voti == "personalizzato" && ($singola_materia['tipo_voto_personalizzato'] == '2' || $singola_materia['tipo_voto_personalizzato'] == '3') )
                                    ) {
                                        /*    ($tipo_visualizzazione_voti != "voto_singolo")
                                        ||
                                        (
                                        ($tipo_visualizzazione_voti == "personalizzato")
                                        &&
                                        ($singola_materia['tipo_voto_personalizzato'] != '1')
                                        )
                                        ) */
                                        // voto_orale_pagellina
                                        $somma_voti_pagella = $somma_voti_pagella + $elenco_voti_pagelline[$cont][51];
                                        $tot_voti_pagella = $tot_voti_pagella + 1;
                                    }
                                }
                                //}}} </editor-fold>
                                //{{{ <editor-fold defaultstate="collapsed" desc="asterischi debiti">
                                if ($elenco_voti_pagelline[$cont][7] == "D") {
                                    $elenco_voti_pagelline[$cont][51] = "*" . $elenco_voti_pagelline[$cont][51];
                                }
                                if ($elenco_voti_pagelline[$cont][7] == "A") {
                                    $elenco_voti_pagelline[$cont][51] = "**" . $elenco_voti_pagelline[$cont][51];
                                }
                                //}}} </editor-fold>
                            }

                            //controllo sul codice di significati_voto
                            if ($elenco_voti_pagelline[$cont][62] == "" || $elenco_voti_pagelline[$cont][62] == "-1") {
                                $elenco_voti_pagelline[$cont][62] = $commento_al_non_voto;
                            } else {
                                //{{{ <editor-fold defaultstate="collapsed" desc="somme per calcolo media">
                                if ($elenco_voti_pagelline[$cont]['in_media_pagelle'] == 'SI') {
                                    if ($tipo_visualizzazione_voti != "voto_singolo" || ( $tipo_visualizzazione_voti == "personalizzato" && $singola_materia['tipo_voto_personalizzato'] == '3' )
                                    ) {
                                        /*    ($tipo_visualizzazione_voti != "voto_singolo")
                                        ||
                                        (
                                        ($tipo_visualizzazione_voti == "personalizzato")
                                        &&
                                        ($singola_materia['tipo_voto_personalizzato'] != '1')
                                        )
                                        ) */
                                        // voto_pratico_pagellina
                                        $somma_voti_pagella = $somma_voti_pagella + $elenco_voti_pagelline[$cont][52];
                                        $tot_voti_pagella = $tot_voti_pagella + 1;
                                    }
                                }
                                //}}} </editor-fold>
                                //{{{ <editor-fold defaultstate="collapsed" desc="asterischi debiti">
                                if ($elenco_voti_pagelline[$cont][7] == "D") {
                                    $elenco_voti_pagelline[$cont][52] = "*" . $elenco_voti_pagelline[$cont][52];
                                }
                                if ($elenco_voti_pagelline[$cont][7] == "A") {
                                    $elenco_voti_pagelline[$cont][52] = "**" . $elenco_voti_pagelline[$cont][52];
                                }
                                //}}} </editor-fold>
                            }
                            //}}} </editor-fold>
                        }

                        //{{{ <editor-fold defaultstate="collapsed" desc="in caso di esonero da religione">
                        if ($singola_materia['tipo_materia'] == "RELIGIONE" && $dati_studente['esonero_religione'] == "1") {
                            $elenco_voti_pagelline[$cont][12] = "";
                            $elenco_voti_pagelline[$cont][10] = "N.A.";
                            $elenco_voti_pagelline[$cont][1000] = "N.A.";
                            $elenco_voti_pagelline[$cont][110] = "N.A.";
                            $elenco_voti_pagelline[$cont][6] = "N.A.";
                            $elenco_voti_pagelline[$cont][50] = "N.A.";
                            $elenco_voti_pagelline[$cont][51] = "N.A.";
                            $elenco_voti_pagelline[$cont][52] = "N.A.";
                            $elenco_voti_pagelline[$cont][60] = "N.A.";
                            $elenco_voti_pagelline[$cont][61] = "N.A.";
                            $elenco_voti_pagelline[$cont][62] = "N.A.";
                            $elenco_voti_pagelline[$cont][160] = "N.A.";
                            $elenco_voti_pagelline[$cont][161] = "N.A.";
                            $elenco_voti_pagelline[$cont][162] = "N.A.";
                            $elenco_voti_pagelline[$cont][6000] = "N.A.";
                            $elenco_voti_pagelline[$cont][6100] = "N.A.";
                            $elenco_voti_pagelline[$cont][6200] = "N.A.";
                            $elenco_voti_pagelline[$cont]['voto_pagellina'] = "N.A.";
                        }
                        //}}} </editor-fold>
                        //{{{ <editor-fold defaultstate="collapsed" desc="in caso di non stampa di religione">
                        if (($elenco_voti_pagelline[$cont][1] == "REL" || $elenco_voti_pagelline[$cont]['tipo_materia'] == 'RELIGIONE') && ($stampo_religione == "NO")) {
                            $elenco_voti_pagelline[$cont][12] = "";
                            $elenco_voti_pagelline[$cont][10] = "";
                            $elenco_voti_pagelline[$cont][1000] = "";
                            $elenco_voti_pagelline[$cont][110] = "";
                            $elenco_voti_pagelline[$cont][6] = "";
                            $elenco_voti_pagelline[$cont][50] = "";
                            $elenco_voti_pagelline[$cont][51] = "";
                            $elenco_voti_pagelline[$cont][52] = "";
                            $elenco_voti_pagelline[$cont][60] = "";
                            $elenco_voti_pagelline[$cont][61] = "";
                            $elenco_voti_pagelline[$cont][62] = "";
                            $elenco_voti_pagelline[$cont][160] = "";
                            $elenco_voti_pagelline[$cont][161] = "";
                            $elenco_voti_pagelline[$cont][162] = "";
                            $elenco_voti_pagelline[$cont][6000] = "";
                            $elenco_voti_pagelline[$cont][6100] = "";
                            $elenco_voti_pagelline[$cont][6200] = "";
                            $elenco_voti_pagelline[$cont]['voto_pagellina'] = '';
                        }
                        //}}} </editor-fold>
                        //{{{ <editor-fold defaultstate="collapsed" desc="in caso di esonero da ed. fisica">
                        if (($elenco_voti_pagelline[$cont][0] == $id_edfisica) && ($dati_studente[26] == "1")) {
                            $elenco_voti_pagelline[$cont][12] = "";
                            $elenco_voti_pagelline[$cont][10] = "----";
                            $elenco_voti_pagelline[$cont][1000] = "----";
                            $elenco_voti_pagelline[$cont][110] = "----";
                            $elenco_voti_pagelline[$cont][6] = "----";
                            $elenco_voti_pagelline[$cont][50] = "----";
                            $elenco_voti_pagelline[$cont][51] = "----";
                            $elenco_voti_pagelline[$cont][52] = "----";
                            $elenco_voti_pagelline[$cont][60] = "----";
                            $elenco_voti_pagelline[$cont][61] = "----";
                            $elenco_voti_pagelline[$cont][62] = "----";
                            $elenco_voti_pagelline[$cont][160] = "----";
                            $elenco_voti_pagelline[$cont][161] = "----";
                            $elenco_voti_pagelline[$cont][162] = "----";
                            $elenco_voti_pagelline[$cont][6000] = "----";
                            $elenco_voti_pagelline[$cont][6100] = "----";
                            $elenco_voti_pagelline[$cont][6200] = "----";
                        }
                        //}}} </editor-fold>

                        if ($stampa_tutti_voti == "SI") {
                            $carenze_trentino = false;
                            if ($trentino_abilitato == 'SI' && !empty(estrai_debiti_studente($id_studente, $anno_scolastico))) {
                                $carenze_trentino = true;
                            }

                            if ($periodo != '11' || ( $periodo == '11' && $elenco_voti_pagelline[$cont]['in_media_pagelle'] == 'SI' )
                            ) {
                                //{{{ <editor-fold defaultstate="collapsed" desc="stampa dei singoli voti del tabellone">
                                //{{{ <editor-fold defaultstate="collapsed" desc="voto singolo">
                                if (( ($tipo_visualizzazione_voti == "voto_singolo") || ( ($tipo_visualizzazione_voti == "personalizzato") && ($singola_materia['tipo_voto_personalizzato'] == '1') ) ) && ($visualizza_assenze != "SOLO_ASSENZE")
                                ) {
                                    if (($visualizza_bocciati == "NO") && ($passato == "NO") && ($carenze_trentino == false || $esito_manuale_superiori == 'SI') && ($da_estero != 'SI')) {
                                        $pdf->Cell($dimensione_mezza_cella, $altezza_cella, '', 'LTB', 0, 'C', $fill);
                                        $pdf->Cell($dimensione_mezza_cella, $altezza_cella, '', 'TBR', 0, 'C', $fill);
                                        $testo_excel .= " " . "	";
                                    } elseif (($visualizza_sospesi == "NO") && ($passato == "NI") && ($carenze_trentino == false || $esito_manuale_superiori == 'SI') && ($da_estero != 'SI')) {
                                        $pdf->Cell($dimensione_mezza_cella, $altezza_cella, '', 'LTB', 0, 'C', $fill);
                                        $pdf->Cell($dimensione_mezza_cella, $altezza_cella, '', 'TBR', 0, 'C', $fill);
                                        $testo_excel .= " " . "	";
                                    } elseif ($visualizza_ammesso_con_insufficienze == 'NO' && strpos(strtoupper($esito['esito']), 'AMMESSO SECONDO O.M.') !== false && $tot_insuff > 0 && ($da_estero != 'SI')) {
                                        //                                        echo $dati_studente['cognome'].' '.$dati_studente['nome'].' '.$tot_insuff,'<br>';
                                        $pdf->Cell($dimensione_mezza_cella, $altezza_cella, '', 'LTB', 0, 'C', $fill);
                                        $pdf->Cell($dimensione_mezza_cella, $altezza_cella, '', 'TBR', 0, 'C', $fill);
                                        $testo_excel .= " " . "	";
                                    } else {
                                        if (($elenco_voti_pagelline[$cont][6] < $voto_minimo_suff) && ($stampa_voti_in_rosso == 'SI')) {
                                            $pdf->SetTextColor(200, 60, 60);
                                        } else {
                                            $pdf->SetTextColor(0, 0, 0);
                                        }
                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);

                                            if (($elenco_voti_pagelline[$cont][6] < 6)) {
                                                $pdf->SetFillColor(255, 255, 0);
                                            }
                                            if (($elenco_voti_pagelline[$cont][6] < 5)) {
                                                $pdf->SetFillColor(255, 0, 0);
                                                $pdf->SetTextColor(255, 255, 255);
                                            }
                                            if (($elenco_voti_pagelline[$cont][6] == '')) {
                                                $pdf->SetFillColor(255, 255, 255);
                                            }
                                        }
                                        //Fra, 06/06/2019 Per le prime professionali dove l'esito è Ammesso alla classe seconda con modifica del PFI
                                        //lo trasformiamo in 6*. Fatto velocemente ovviamente perchè serve subito ma da migliorare!!!
                                        // Si deve aggiungere controllo su quando lo stai stampando, altrimenti gli storici non vanno più bene e
                                        //magari aggiungere un parametro di stampa (adesso fa cosi per tutte le prime professionali di qualsiasi scuola)
                                        // Per adesso commento il nuovo codice perchè non sappiamo se va bene a tutti
                                        //                                        if (
                                        //                                            $controllo_voto_singolo == 'Ammesso alla classe seconda con revisione del PFI'
                                        //                                            and
                                        //                                            ($dati_classe['tipo_indirizzo'] == '2' && $dati_classe['classe'] == 1)
                                        //                                                and
                                        //                                            $elenco_voti_pagelline[$cont][6] < $voto_minimo_suff
                                        //                                                and
                                        //                                                $elenco_voti_pagelline[$cont]['tipo_materia'] != 'RELIGIONE'
                                        //                                                and
                                        //                                                $elenco_voti_pagelline[$cont][0] != $id_edfisica
                                        //                                            )
                                        //                                        {
                                        //                                            $pdf->SetTextColor(0,0,0);
                                        //                                            $elenco_voti_pagelline[$cont][6] = '6*';
                                        //                                            $elenco_voti_pagelline[$cont][10] = '6*';
                                        //                                            $elenco_voti_pagelline[$cont][110] = '6*';
                                        //                                            $elenco_voti_pagelline[$cont][1000] = '6*';
                                        //                                            $elenco_voti_pagelline[$cont]['voto_pagellina'] = '6*';
                                        //                                        }
                                        if ($stampo_debiti_sui_voti != 'NO') {
                                            //{{{ <editor-fold defaultstate="collapsed">
                                            if (is_array($elenco_debiti)) {
                                                foreach ($elenco_debiti as $debito) {
                                                    if (($debito["id_materia"] != "-1") && ($elenco_voti_pagelline[$cont][0] == $debito["id_materia"])) {
                                                        $elenco_voti_pagelline[$cont][6] = $elenco_voti_pagelline[$cont][6] . '*';
                                                        $elenco_voti_pagelline[$cont][10] = $elenco_voti_pagelline[$cont][10] . '*';
                                                        $elenco_voti_pagelline[$cont][110] = $elenco_voti_pagelline[$cont][110] . '*';
                                                        $elenco_voti_pagelline[$cont][1000] = $elenco_voti_pagelline[$cont][1000] . '*';
                                                        $cont_tot_comma4a++;
                                                    }
                                                }
                                            }
                                            //}}} </editor-fold>
                                        }

                                        switch ($tipo_voto_stampa) {
                                        case "scheda":
                                            //{{{ <editor-fold defaultstate="collapsed">
                                            if ($elenco_voti_pagelline[$cont][6] == "-1") {
                                                $voto_da_stampare = $commento_al_non_voto;
                                            } else {
                                                $voto_da_stampare = $elenco_voti_pagelline[$cont][6];
                                            }
                                            //}}} </editor-fold>
                                            break;
                                        case "codice":
                                            $voto_da_stampare = $elenco_voti_pagelline[$cont][10];
                                            break;
                                        case "valore_descrizione":
                                            //{{{ <editor-fold defaultstate="collapsed">
                                            $voto_da_stampare = $elenco_voti_pagelline[$cont][1000];
                                            //}}} </editor-fold>
                                            break;
                                        case "valore_pagella":
                                            //{{{ <editor-fold defaultstate="collapsed">
                                            if ($elenco_voti_pagelline[$cont][110] == "-1") {
                                                $voto_da_stampare = $commento_al_non_voto;
                                            } else {
                                                $voto_da_stampare = $elenco_voti_pagelline[$cont][110];
                                            }
                                            //}}} </editor-fold>
                                            break;
                                        default:
                                            //{{{ <editor-fold defaultstate="collapsed">

                                            if ($elenco_voti_pagelline[$cont][6] == "-1") {
                                                $voto_da_stampare = $commento_al_non_voto;
                                            } else {
                                                $voto_da_stampare = $elenco_voti_pagelline[$cont][6];
                                            }
                                            //}}} </editor-fold>
                                            break;
                                        }

                                        if ($elenco_voti_pagelline[$cont][7] == "D") {
                                            if ($stampo_debiti_sui_voti == "NO") {
                                                $inserimento_voto_numerico = $voto_da_stampare;
                                            } else {
                                                $inserimento_voto_numerico = "*" . $voto_da_stampare;
                                            }
                                        } else {
                                            if ($elenco_voti_pagelline[$cont][7] == "A") {
                                                if ($stampo_debiti_sui_voti == "NO") {
                                                    $inserimento_voto_numerico = $voto_da_stampare;
                                                } else {
                                                    $inserimento_voto_numerico = "**" . $voto_da_stampare;
                                                }
                                            } else {
                                                $inserimento_voto_numerico = $voto_da_stampare;
                                            }
                                        }


                                        // else
                                        // {
                                        //     $dimensione_cella_old = $dimensione_cella;
                                        //     $dimensione_cella = $dimensione_cella / 2;
                                        // }

                                        if (($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE") && ($elenco_voti_pagelline[$cont]['tipo_materia'] != "CONDOTTA")) {
                                            if ($parametro_debiti == "SI") {
                                                $dimensione_mezza_cella_old = $dimensione_mezza_cella;
                                                $dimensione_cella_old = $dimensione_cella;
                                                $dimensione_mezza_cella = $dimensione_cella / 3;
                                                $dimensione_cella = $dimensione_mezza_cella;
                                            }
                                            if ($trentino_abilitato == 'SI' && $esposizione_archivio == 'esposizione' && in_array(intval($elenco_voti_pagelline[$cont][10]), [1, 2, 3, 4, 5]) && verifica_presenza_debito($id_studente, $elenco_voti_pagelline[$cont]['id_materia'], $anno_scolastico)
                                            ) {
                                                $pdf->SetTextColor(0, 0, 0);
                                                $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, 6, 'LTB', 0, 'C', $fill);
                                            } elseif ($trentino_abilitato == 'SI' && $esposizione_archivio == 'esposizione' && ($elenco_voti_pagelline[$cont][10]) && ($dati_studente['pei'] == 'SI' || $dati_studente['bes'] == 'SI') && (in_array(intval($elenco_voti_pagelline[$cont][10]), [1, 2, 3, 4, 5]) || $elenco_voti_pagelline[$cont][10] == '')
                                            ) {
                                                $pdf->SetTextColor(0, 0, 0);

                                                if ($dati_studente['esonero_religione'] == 1 && $elenco_voti_pagelline[$cont]['tipo_materia'] == "RELIGIONE" && $elenco_voti_pagelline[$cont][10] == '') {
                                                    $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, '', 'LTB', 0, 'C', $fill);
                                                } else {
                                                    $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, 6, 'LTB', 0, 'C', $fill);
                                                }
                                            } else {

                                                $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, $inserimento_voto_numerico, 'LTB', 0, 'C', $fill);
                                            }

                                            if ($celle_colorate == 'SI') {
                                                $pdf->SetFillColor(255, 255, 255);
                                                $pdf->SetTextColor(0, 0, 0);
                                            }
                                            if ($parametro_debiti == "SI") {
                                                $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, $elenco_voti_pagelline[$cont]['tipo_recupero'], 'TB', 0, 'C', $fill);
                                            }

                                            $pdf->SetTextColor(0, 0, 0);

                                            if ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE") {
                                                $tot_ore_assenze_colonna_unica_arrotondate = $tot_ore_assenze_colonna_unica_arrotondate + intval(stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze));
                                                $pdf->CellFitScale($dimensione_mezza_cella,$altezza_cella,stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze),'TBR',0,'C',$fill);
                                                $testo_excel .= str_replace('.',',',$inserimento_voto_numerico) . "	" . str_replace('.',',',stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze)) . "	";
                                            } elseif ($visualizza_assenze == "PERCENTUALE") {

                                                if ($elenco_voti_pagelline[$cont]['monteore_totale'] != 0) {
                                                    $ass_perc_mat = round($elenco_voti_pagelline[$cont][12]/$elenco_voti_pagelline[$cont]['monteore_totale'],4) * 100;
                                                    $pdf->CellFitScale($dimensione_mezza_cella,$altezza_cella, $ass_perc_mat.'%','TBR',0,'C',$fill);
                                                    $testo_excel .= str_replace('.',',',$inserimento_voto_numerico)."\t";
                                                    $testo_excel .= "$ass_perc_mat%	";
                                                } else { // monteore 0
                                                    $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, '0%', 'TBR', 0, 'C', $fill);
                                                    $testo_excel .= str_replace('.', ',', $inserimento_voto_numerico) . "\t";
                                                    $testo_excel .= "0%	";
                                                }
                                            }
                                        } else {
                                            $bordo = 'LTBR';
                                            if ($parametro_debiti == "SI") {
                                                $dimensione_mezza_cella_old = $dimensione_mezza_cella;
                                                $dimensione_cella_old = $dimensione_cella;
                                                $dimensione_mezza_cella = $dimensione_cella / 2;
                                                $dimensione_cella = $dimensione_mezza_cella;
                                                $bordo = 'LTB';
                                            }
                                            if ($trentino_abilitato == 'SI' && $esposizione_archivio == 'esposizione' && in_array(intval($elenco_voti_pagelline[$cont][10]), [1, 2, 3, 4, 5]) && verifica_presenza_debito($id_studente, $elenco_voti_pagelline[$cont]['id_materia'], $anno_scolastico)
                                            ) {
                                                $pdf->SetTextColor(0, 0, 0);
                                                $pdf->CellFitScale($dimensione_cella, $altezza_cella, 6, 'LTBR', 0, 'C', $fill);
                                            } elseif ($trentino_abilitato == 'SI' && $esposizione_archivio == 'esposizione' && ($elenco_voti_pagelline[$cont][10]) && ($dati_studente['pei'] == 'SI' || $dati_studente['bes'] == 'SI') && (in_array(intval($elenco_voti_pagelline[$cont][10]), [1, 2, 3, 4, 5]) || $elenco_voti_pagelline[$cont][10] == '')
                                            ) {
                                                $pdf->SetTextColor(0, 0, 0);

                                                if ($dati_studente['esonero_religione'] == 1 && $elenco_voti_pagelline[$cont]['tipo_materia'] == "RELIGIONE" && $elenco_voti_pagelline[$cont][10] == '') {
                                                    $pdf->CellFitScale($dimensione_cella, $altezza_cella, '', 'LTB', 0, 'C', $fill);
                                                } else {
                                                    $pdf->CellFitScale($dimensione_cella, $altezza_cella, 6, 'LTB', 0, 'C', $fill);
                                                }
                                            } else {
                                                $pdf->CellFitScale($dimensione_cella, $altezza_cella, $inserimento_voto_numerico, $bordo, 0, 'C', $fill);
                                            }

                                            if ($celle_colorate == 'SI') {
                                                $pdf->SetFillColor(255, 255, 255);
                                                $pdf->SetTextColor(0, 0, 0);
                                            }
                                            if ($parametro_debiti == "SI") {
                                                $pdf->CellFitScale($dimensione_cella, $altezza_cella, $elenco_voti_pagelline[$cont]['tipo_recupero'], 'TBR', 0, 'C', $fill);
                                            }

                                            $pdf->SetTextColor(0, 0, 0);
                                            $testo_excel .= str_replace('.', ',', $inserimento_voto_numerico) . "	";
                                        }
                                        if ($parametro_debiti == "SI") {
                                            $dimensione_mezza_cella = $dimensione_mezza_cella_old;
                                            $dimensione_cella = $dimensione_cella_old;
                                        }
                                    }
                                }
                                //}}} </editor-fold>
                                //setto il margine della cella al valore scelto dall'utente
                                $pdf->cMargin = $valore_margine_cella_voti;
                                //{{{ <editor-fold defaultstate="collapsed" desc="scritto orale pratico e solo scritto e orale">
                                if (( ($tipo_visualizzazione_voti == "scritto_orale") || ($tipo_visualizzazione_voti == "scritto_orale_pratico") || ( ($tipo_visualizzazione_voti == "personalizzato") && ( ($singola_materia['tipo_voto_personalizzato'] == '3') || ($singola_materia['tipo_voto_personalizzato'] == '0') || ($singola_materia['tipo_voto_personalizzato'] == '2') ) ) ) && ( ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE") || ($visualizza_assenze == "NO" || $visualizza_assenze == "SOLO_TOTALE") )
                                ) {
                                    if (($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")) {
                                        if ($tipo_visualizzazione_voti == "scritto_orale" || ($tipo_visualizzazione_voti == "personalizzato" && $singola_materia['tipo_voto_personalizzato'] == '2')
                                        ) {
                                            if ($parametro_debiti == "SI") {
                                                $dimensione_cella_sub = $dimensione_cella / 4;
                                            } else {
                                                $dimensione_cella_sub = $dimensione_cella / 3;
                                            }
                                            $dimensione_cella_condotta = $dimensione_cella / 2;
                                        } else {
                                            if ($parametro_debiti == "SI") {
                                                $dimensione_cella_sub = $dimensione_cella / 5;
                                            } else {
                                                $dimensione_cella_sub = $dimensione_cella / 4;
                                            }
                                            $dimensione_cella_condotta = $dimensione_cella / 3;
                                        }
                                    } else {
                                        if ($tipo_visualizzazione_voti == "scritto_orale" || ($tipo_visualizzazione_voti == "personalizzato" && $singola_materia['tipo_voto_personalizzato'] == '2')
                                        ) {
                                            if ($parametro_debiti == "SI") {
                                                $dimensione_cella_sub = $dimensione_cella / 3;
                                            } else {
                                                $dimensione_cella_sub = $dimensione_cella / 2;
                                            }
                                            $dimensione_cella_condotta = $dimensione_cella / 2;
                                        } else {
                                            if ($parametro_debiti == "SI") {
                                                $dimensione_cella_sub = $dimensione_cella / 4;
                                            } else {
                                                $dimensione_cella_sub = $dimensione_cella / 3;
                                            }
                                            $dimensione_cella_condotta = $dimensione_cella / 3;
                                        }
                                    }

                                    switch ($tipo_voto_stampa) {
                                    case "scheda":
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        $voto_scritto_da_stampare = $elenco_voti_pagelline[$cont][50];
                                        $voto_orale_da_stampare = $elenco_voti_pagelline[$cont][51];
                                        $voto_pratico_da_stampare = $elenco_voti_pagelline[$cont][52];
                                        //}}} </editor-fold>
                                        break;
                                    case "codice":
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        $voto_scritto_da_stampare = $elenco_voti_pagelline[$cont][60];
                                        $voto_orale_da_stampare = $elenco_voti_pagelline[$cont][61];
                                        $voto_pratico_da_stampare = $elenco_voti_pagelline[$cont][62];
                                        //}}} </editor-fold>
                                        break;
                                    case "valore_descrizione":
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        $voto_scritto_da_stampare = $elenco_voti_pagelline[$cont][6000];
                                        $voto_orale_da_stampare = $elenco_voti_pagelline[$cont][6100];
                                        $voto_pratico_da_stampare = $elenco_voti_pagelline[$cont][6200];
                                        //}}} </editor-fold>
                                        break;
                                    case "valore_pagella":
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        $voto_scritto_da_stampare = $elenco_voti_pagelline[$cont][160];
                                        $voto_orale_da_stampare = $elenco_voti_pagelline[$cont][161];
                                        $voto_pratico_da_stampare = $elenco_voti_pagelline[$cont][162];
                                        //}}} </editor-fold>
                                        break;
                                    default:
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        $voto_scritto_da_stampare = $elenco_voti_pagelline[$cont][50];
                                        $voto_orale_da_stampare = $elenco_voti_pagelline[$cont][51];
                                        $voto_pratico_da_stampare = $elenco_voti_pagelline[$cont][52];
                                        //}}} </editor-fold>
                                        break;
                                    }

                                    if ($celle_colorate == 'SI') {
                                        $pdf->SetFillColor(255, 255, 255);
                                    }

                                    if (($elenco_voti_pagelline[$cont]['tipo_materia'] != "CONDOTTA")) {

                                        if (($elenco_voti_pagelline[$cont][50] < $voto_minimo_suff) && ($stampa_voti_in_rosso == 'SI')) {
                                            $pdf->SetTextColor(200, 60, 60);

                                            if ($celle_colorate == 'SI') {
                                                $pdf->SetTextColor(0, 0, 0);
                                            }
                                        } elseif ($celle_colorate != 'SI') {
                                            $pdf->SetTextColor(0, 0, 0);
                                        }

                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);

                                            if (($elenco_voti_pagelline[$cont][50] < 6)) {
                                                $pdf->SetFillColor(255, 255, 0);
                                            }
                                            if (($elenco_voti_pagelline[$cont][50] < 5)) {
                                                $pdf->SetFillColor(255, 0, 0);
                                                $pdf->SetTextColor(255, 255, 255);
                                            }
                                            if (($elenco_voti_pagelline[$cont][50] == '')) {
                                                $pdf->SetFillColor(255, 255, 255);
                                            }
                                        }

                                        $pdf->CellFitScale($dimensione_cella_sub, $altezza_cella, $voto_scritto_da_stampare, 'LTB', 0, 'C', $fill);
                                        $pdf->SetTextColor(0, 0, 0);


                                        if (($elenco_voti_pagelline[$cont][51] < $voto_minimo_suff) && ($stampa_voti_in_rosso == 'SI')) {
                                            $pdf->SetTextColor(200, 60, 60);

                                            if ($celle_colorate == 'SI') {
                                                $pdf->SetTextColor(0, 0, 0);
                                            }
                                        } elseif ($celle_colorate != 'SI') {
                                            $pdf->SetTextColor(0, 0, 0);
                                        }

                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);

                                            if (($elenco_voti_pagelline[$cont][51] < 6)) {
                                                $pdf->SetFillColor(255, 255, 0);
                                            }
                                            if (($elenco_voti_pagelline[$cont][51] < 5)) {
                                                $pdf->SetFillColor(255, 0, 0);
                                                $pdf->SetTextColor(255, 255, 255);
                                            }
                                            if (($elenco_voti_pagelline[$cont][51] == '')) {
                                                $pdf->SetFillColor(255, 255, 255);
                                            }
                                        }
                                        $pdf->CellFitScale($dimensione_cella_sub, $altezza_cella, $voto_orale_da_stampare, 'TB', 0, 'C', $fill);
                                        $pdf->SetTextColor(0, 0, 0);

                                        //echo $tipo_visualizzazione_voti;
                                        if ($tipo_visualizzazione_voti == "scritto_orale_pratico" || ( $tipo_visualizzazione_voti == "personalizzato" && ($singola_materia['tipo_voto_personalizzato'] == '3' || $singola_materia['tipo_voto_personalizzato'] == '0' ) )
                                        ) {
                                            if (($elenco_voti_pagelline[$cont][52] < $voto_minimo_suff) && ($stampa_voti_in_rosso == 'SI')) {
                                                $pdf->SetTextColor(200, 60, 60);

                                                if ($celle_colorate == 'SI') {
                                                    $pdf->SetTextColor(0, 0, 0);
                                                }
                                            } elseif ($celle_colorate != 'SI') {
                                                $pdf->SetTextColor(0, 0, 0);
                                            }

                                            if ($celle_colorate == 'SI') {
                                                $pdf->SetFillColor(255, 255, 255);

                                                if (($elenco_voti_pagelline[$cont][52] < 6)) {
                                                    $pdf->SetFillColor(255, 255, 0);
                                                }
                                                if (($elenco_voti_pagelline[$cont][52] < 5)) {
                                                    $pdf->SetFillColor(255, 0, 0);
                                                    $pdf->SetTextColor(255, 255, 255);
                                                }
                                                if (($elenco_voti_pagelline[$cont][52] == '')) {
                                                    $pdf->SetFillColor(255, 255, 255);
                                                }
                                            }
                                            $pdf->CellFitScale($dimensione_cella_sub, $altezza_cella, $voto_pratico_da_stampare, 'TB', 0, 'C', $fill);
                                            $pdf->SetTextColor(0, 0, 0);
                                            $testo_excel .= str_replace('.', ',', $elenco_voti_pagelline[$cont][50]) . "	" . str_replace('.', ',', $elenco_voti_pagelline[$cont][51]) . "	" . str_replace('.', ',', $elenco_voti_pagelline[$cont][52]) . "	";
                                        } else {
                                            $testo_excel .= str_replace('.', ',', $elenco_voti_pagelline[$cont][50]) . "	" . str_replace('.', ',', $elenco_voti_pagelline[$cont][51]) . "	";
                                        }

                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);
                                        }

                                        if (($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE")) {
                                            if ($parametro_debiti == "SI") {
                                                $pdf->CellFitScale($dimensione_cella_sub, $altezza_cella, $elenco_voti_pagelline[$cont]['tipo_recupero'], 'TB', 0, 'C', $fill);
                                                $testo_excel .= $elenco_voti_pagelline[$cont]['tipo_recupero'] . "	";
                                            }

                                            if ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE") {
//                                                $tot_ore_assenze_colonna_unica_arrotondate = $tot_ore_assenze_colonna_unica_arrotondate + intval(stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze));
                                                $pdf->CellFitScale($dimensione_cella_sub,$altezza_cella,stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze),'TBR',0,'C',$fill);
                                                $testo_excel .= str_replace('.',',',stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze)) . "	";
                                            }
                                            elseif ($visualizza_assenze == "PERCENTUALE") {

                                                if ($elenco_voti_pagelline[$cont]['monteore_totale'] != 0)
                                                {
                                                    $ass_perc_mat = round($elenco_voti_pagelline[$cont][12]/$elenco_voti_pagelline[$cont]['monteore_totale'],4) * 100;
                                                    $pdf->CellFitScale($dimensione_cella_sub,$altezza_cella, $ass_perc_mat.'%','TBR',0,'C',$fill);
                                                    $testo_excel .= "$ass_perc_mat%	";
                                                } else { // monteore 0
                                                    $pdf->CellFitScale($dimensione_cella_sub, $altezza_cella, '0%', 'TBR', 0, 'C', $fill);
                                                    $testo_excel .= "0%	";
                                                }
                                            }
                                        } else {
                                            if ($parametro_debiti == "SI") {
                                                $pdf->CellFitScale($dimensione_cella_sub, $altezza_cella, $elenco_voti_pagelline[$cont]['tipo_recupero'], 'TBR', 0, 'C', $fill);
                                                $testo_excel .= $elenco_voti_pagelline[$cont]['tipo_recupero'] . "	";
                                            }
                                        }
                                    } else {
                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);

                                            if (($elenco_voti_pagelline[$cont][50] < 6)) {
                                                $pdf->SetFillColor(255, 255, 0);
                                            }
                                            if (($elenco_voti_pagelline[$cont][50] < 5)) {
                                                $pdf->SetFillColor(255, 0, 0);
                                                $pdf->SetTextColor(255, 255, 255);
                                            }
                                            if (($elenco_voti_pagelline[$cont][50] == '')) {
                                                $pdf->SetFillColor(255, 255, 255);
                                            }
                                        }

                                        if (($elenco_voti_pagelline[$cont][50] < $voto_minimo_suff) && ($stampa_voti_in_rosso == 'SI')) {
                                            $pdf->SetTextColor(200, 60, 60);

                                            if ($celle_colorate == 'SI') {
                                                $pdf->SetTextColor(0, 0, 0);
                                            }
                                        } elseif ($celle_colorate != 'SI') {
                                            $pdf->SetTextColor(0, 0, 0);
                                        }

                                        $pdf->CellFitScale($dimensione_cella_condotta, $altezza_cella, $voto_scritto_da_stampare, 'LTB', 0, 'C', $fill);
                                        $pdf->SetTextColor(0, 0, 0);

                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);

                                            if (($elenco_voti_pagelline[$cont][51] < 6)) {
                                                $pdf->SetFillColor(255, 255, 0);
                                            }
                                            if (($elenco_voti_pagelline[$cont][51] < 5)) {
                                                $pdf->SetFillColor(255, 0, 0);
                                                $pdf->SetTextColor(255, 255, 255);
                                            }
                                            if (($elenco_voti_pagelline[$cont][51] == '')) {
                                                $pdf->SetFillColor(255, 255, 255);
                                            }
                                        }

                                        if (($elenco_voti_pagelline[$cont][51] < $voto_minimo_suff) && ($stampa_voti_in_rosso == 'SI')) {
                                            $pdf->SetTextColor(200, 60, 60);

                                            if ($celle_colorate == 'SI') {
                                                $pdf->SetTextColor(0, 0, 0);
                                            }
                                        } elseif ($celle_colorate != 'SI') {
                                            $pdf->SetTextColor(0, 0, 0);
                                        }

                                        $pdf->CellFitScale($dimensione_cella_condotta, $altezza_cella, $voto_orale_da_stampare, 'TB', 0, 'C', $fill);
                                        $pdf->SetTextColor(0, 0, 0);

                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);

                                            if (($elenco_voti_pagelline[$cont][52] < 6)) {
                                                $pdf->SetFillColor(255, 255, 0);
                                            }
                                            if (($elenco_voti_pagelline[$cont][52] < 5)) {
                                                $pdf->SetFillColor(255, 0, 0);
                                                $pdf->SetTextColor(255, 255, 255);
                                            }
                                            if (($elenco_voti_pagelline[$cont][52] == '')) {
                                                $pdf->SetFillColor(255, 255, 255);
                                            }
                                        }

                                        if ($tipo_visualizzazione_voti == "scritto_orale_pratico" || ($tipo_visualizzazione_voti == "personalizzato" && $singola_materia['tipo_voto_personalizzato'] == '3')
                                        ) {
                                            if (($elenco_voti_pagelline[$cont][52] < $voto_minimo_suff) && ($stampa_voti_in_rosso == 'SI')) {
                                                $pdf->SetTextColor(200, 60, 60);

                                                if ($celle_colorate == 'SI') {
                                                    $pdf->SetTextColor(0, 0, 0);
                                                }
                                            } elseif ($celle_colorate != 'SI') {
                                                $pdf->SetTextColor(0, 0, 0);
                                            }

                                            $pdf->CellFitScale($dimensione_cella_condotta, $altezza_cella, $voto_pratico_da_stampare, 'TBR', 0, 'C', $fill);
                                            $pdf->SetTextColor(0, 0, 0);
                                            $testo_excel .= str_replace('.', ',', $elenco_voti_pagelline[$cont][50]) . "	" . str_replace('.', ',', $elenco_voti_pagelline[$cont][51]) . "	" . str_replace('.', ',', $elenco_voti_pagelline[$cont][52]) . "	";
                                        } else {
                                            $testo_excel .= str_replace('.', ',', $elenco_voti_pagelline[$cont][50]) . "	" . str_replace('.', ',', $elenco_voti_pagelline[$cont][51]) . "	";
                                        }

                                        if ($celle_colorate == 'SI') {
                                            $pdf->SetFillColor(255, 255, 255);
                                        }
                                    }
								}
								//}}} </editor-fold>

								if($visualizza_assenze == "SOLO_ASSENZE") {
                                    $tot_ore_assenze_colonna_unica_arrotondate = $tot_ore_assenze_colonna_unica_arrotondate + intval(stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze));
									$pdf->CellFitScale($dimensione_cella, $altezza_cella, stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze), 'TBRL', 0, 'C', $fill);
									$testo_excel .= str_replace('.', ',', stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze)) . "	";
									$tot_ore_assenze = $tot_ore_assenze + $elenco_voti_pagelline[$cont][12];
								}

                                if($visualizza_assenze == "SOLO_TOTALE" ) {
                                    $tot_ore_assenze_colonna_unica_arrotondate = $tot_ore_assenze_colonna_unica_arrotondate + intval(stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze));
								}

                                if ($visualizza_assenze == "PERCENTUALE") {
                                    if ($elenco_voti_pagelline[$cont]['monteore_totale'] != 0) {
                                        $tot_monteore_assenze += $elenco_voti_pagelline[$cont]['monteore_totale'];
                                        $tot_ore_assenze = $tot_ore_assenze + $elenco_voti_pagelline[$cont][12];
                                    }
                                }

                                $pdf->cMargin = $margine_cella;
                                //}}} </editor-fold>
                            }
                        } elseif ($stampa_tutti_voti == 'SOLO_RECUPERI') {

                            $carenze_trentino = false;
                            if ($trentino_abilitato == 'SI' && !empty(estrai_debiti_studente($id_studente, $anno_scolastico))) {
                                $carenze_trentino = true;
                            }

                            if ($periodo != '11' || ( $periodo == '11' && $elenco_voti_pagelline[$cont]['in_media_pagelle'] == 'SI' )
                            ) {
                                //{{{ <editor-fold defaultstate="collapsed" desc="stampa dei singoli recuperi del tabellone">
                                //{{{ <editor-fold defaultstate="collapsed" desc="voto singolo">
                                //                                if  (
                                //                                        (
                                //                                            ($tipo_visualizzazione_voti == "voto_singolo")
                                //                                            ||
                                //                                            (
                                //                                                ($tipo_visualizzazione_voti == "personalizzato")
                                //                                                &&
                                //                                                ($singola_materia['tipo_voto_personalizzato'] == '1')
                                //                                            )
                                //                                        )
                                //                                        &&
                                //                                        ($visualizza_assenze != "SOLO_ASSENZE")
                                //                                    )
                                //                                {
                                //                                    if(($visualizza_bocciati == "NO") && ($passato == "NO") && $carenze_trentino == false)
                                //                                    {
                                //                                        $pdf->Cell($dimensione_mezza_cella,$altezza_cella,'','LTB',0,'C',$fill);
                                //                                        $pdf->Cell($dimensione_mezza_cella,$altezza_cella,'','TBR',0,'C',$fill);
                                //                                        $testo_excel .= " " . "    ";
                                //                                    }
                                //                                    elseif(($visualizza_sospesi == "NO") && ($passato == "NI") && $carenze_trentino == false)
                                //                                    {
                                //                                        $pdf->Cell($dimensione_mezza_cella,$altezza_cella,'','LTB',0,'C',$fill);
                                //                                        $pdf->Cell($dimensione_mezza_cella,$altezza_cella,'','TBR',0,'C',$fill);
                                //                                        $testo_excel .= " " . "    ";
                                //                                    }
                                //                                    elseif($visualizza_ammesso_con_insufficienze == 'NO' && strpos(strtoupper($esito['esito']), 'AMMESSO SECONDO O.M.') !== false && $tot_insuff > 0)
                                //                                    {
                                ////                                        echo $dati_studente['cognome'].' '.$dati_studente['nome'].' '.$tot_insuff,'<br>';
                                //                                        $pdf->Cell($dimensione_mezza_cella,$altezza_cella,'','LTB',0,'C',$fill);
                                //                                        $pdf->Cell($dimensione_mezza_cella,$altezza_cella,'','TBR',0,'C',$fill);
                                //                                        $testo_excel .= " " . "    ";
                                //                                    }
                                //                                    else
                                //                                    {
                                //                                        if($stampo_debiti_sui_voti != 'NO')
                                //                                        {
                                //                                            //{{{ <editor-fold defaultstate="collapsed">
                                //                                            if(is_array($elenco_debiti))
                                //                                            {
                                //                                                foreach($elenco_debiti as $debito)
                                //                                                {
                                //                                                    if(($debito["id_materia"] != "-1") && ($elenco_voti_pagelline[$cont][0] == $debito["id_materia"]))
                                //                                                    {
                                //                                                        $elenco_voti_pagelline[$cont][6] = $elenco_voti_pagelline[$cont][6] . '*';
                                //                                                        $elenco_voti_pagelline[$cont][10] = $elenco_voti_pagelline[$cont][10] . '*';
                                //                                                        $elenco_voti_pagelline[$cont][110] = $elenco_voti_pagelline[$cont][110] . '*';
                                //                                                        $elenco_voti_pagelline[$cont][1000] = $elenco_voti_pagelline[$cont][1000] . '*';
                                //                                                        $cont_tot_comma4a++;
                                //                                                    }
                                //                                                }
                                //                                            }
                                //                                            //}}} </editor-fold>
                                //                                        }
                                //                                        switch($tipo_voto_stampa)
                                //                                        {
                                //                                            case "scheda":
                                //                                                //{{{ <editor-fold defaultstate="collapsed">
                                //                                                if($elenco_voti_pagelline[$cont][6] == "-1")
                                //                                                {
                                //                                                    $voto_da_stampare = $commento_al_non_voto;
                                //                                                }
                                //                                                else
                                //                                                {
                                //                                                    $voto_da_stampare = $elenco_voti_pagelline[$cont][6];
                                //                                                }
                                //                                                //}}} </editor-fold>
                                //                                                break;
                                //                                            case "codice":
                                //                                                $voto_da_stampare = $elenco_voti_pagelline[$cont][10];
                                //                                                break;
                                //                                            case "valore_descrizione":
                                //                                                //{{{ <editor-fold defaultstate="collapsed">
                                //                                                $voto_da_stampare = $elenco_voti_pagelline[$cont][1000];
                                //                                                //}}} </editor-fold>
                                //                                                break;
                                //                                            case "valore_pagella":
                                //                                                //{{{ <editor-fold defaultstate="collapsed">
                                //                                                if($elenco_voti_pagelline[$cont][110] == "-1")
                                //                                                {
                                //                                                    $voto_da_stampare = $commento_al_non_voto;
                                //                                                }
                                //                                                else
                                //                                                {
                                //                                                    $voto_da_stampare = $elenco_voti_pagelline[$cont][110];
                                //                                                }
                                //                                                //}}} </editor-fold>
                                //                                                break;
                                //                                            default:
                                //                                                //{{{ <editor-fold defaultstate="collapsed">
                                //
                                //                                                if($elenco_voti_pagelline[$cont][6] == "-1")
                                //                                                {
                                //                                                    $voto_da_stampare = $commento_al_non_voto;
                                //                                                }
                                //                                                else
                                //                                                {
                                //                                                    $voto_da_stampare = $elenco_voti_pagelline[$cont][6];
                                //                                                }
                                //                                                //}}} </editor-fold>
                                //                                                break;
                                //                                        }
                                //
                                //                                        if($elenco_voti_pagelline[$cont][7] == "D")
                                //                                        {
                                //                                            if($stampo_debiti_sui_voti == "NO")
                                //                                            {
                                //                                                $inserimento_voto_numerico = $voto_da_stampare;
                                //                                            }
                                //                                            else
                                //                                            {
                                //                                                $inserimento_voto_numerico = "*" . $voto_da_stampare;
                                //                                            }
                                //                                        }
                                //                                        else
                                //                                        {
                                //                                            if($elenco_voti_pagelline[$cont][7] == "A")
                                //                                            {
                                //                                                if($stampo_debiti_sui_voti == "NO")
                                //                                                {
                                //                                                    $inserimento_voto_numerico = $voto_da_stampare;
                                //                                                }
                                //                                                else
                                //                                                {
                                //                                                    $inserimento_voto_numerico = "**" . $voto_da_stampare;
                                //                                                }
                                //                                            }
                                //                                            else
                                //                                            {
                                //                                                $inserimento_voto_numerico = $voto_da_stampare;
                                //                                            }
                                //                                        }
                                // else
                                // {
                                //     $dimensione_cella_old = $dimensione_cella;
                                //     $dimensione_cella = $dimensione_cella / 2;
                                // }

                                if (($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE" || $visualizza_assenze == "PERCENTUALE") && ($elenco_voti_pagelline[$cont]['tipo_materia'] != "CONDOTTA")) {
                                    if ($parametro_debiti == "SI") {
                                        $dimensione_mezza_cella_old = $dimensione_mezza_cella;
                                        $dimensione_cella_old = $dimensione_cella;
                                        $dimensione_mezza_cella = $dimensione_cella / 3;
                                        $dimensione_cella = $dimensione_mezza_cella;
                                    }
                                    //                                            if ($trentino_abilitato == 'SI'
                                    //                                                    && $esposizione_archivio == 'esposizione'
                                    //                                                    && in_array(intval($elenco_voti_pagelline[$cont][10]), [1,2,3,4,5])
                                    //                                                    && verifica_presenza_debito($id_studente,$elenco_voti_pagelline[$cont]['id_materia'], $anno_scolastico)) {
                                    //                                                $pdf->SetTextColor(0,0,0);
                                    //                                                $pdf->CellFitScale($dimensione_mezza_cella,$altezza_cella,6,'LTB',0,'C',$fill);
                                    //                                            } elseif ($trentino_abilitato == 'SI'
                                    //                                                    && $esposizione_archivio == 'esposizione'
                                    //                                                    && ($elenco_voti_pagelline[$cont][10])
                                    //                                                    && ($dati_studente['pei'] == 'SI' || $dati_studente['bes'] == 'SI')
                                    //                                                    && (in_array(intval($elenco_voti_pagelline[$cont][10]), [1,2,3,4,5]) || $elenco_voti_pagelline[$cont][10] == '')) {
                                    //                                                $pdf->SetTextColor(0,0,0);
                                    //
                                    //                                                if ($dati_studente['esonero_religione'] == 1 && $elenco_voti_pagelline[$cont]['tipo_materia'] == "RELIGIONE" && $elenco_voti_pagelline[$cont][10] == '') {
                                    //                                                    $pdf->CellFitScale($dimensione_mezza_cella,$altezza_cella,'','LTB',0,'C',$fill);
                                    //                                                } else {
                                    //                                                    $pdf->CellFitScale($dimensione_mezza_cella,$altezza_cella,6,'LTB',0,'C',$fill);
                                    //                                                }
                                    //                                            } else {

                                    $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, $elenco_voti_pagelline[$cont]['tipo_recupero_tradotto'], 'LTB', 0, 'C', $fill);
                                    //                                            }
                                    //                                            if($parametro_debiti == "SI")
                                    //                                            {
                                    //                                                $pdf->CellFitScale($dimensione_mezza_cella,$altezza_cella,$elenco_voti_pagelline[$cont]['tipo_recupero'],'TB',0,'C',$fill);
                                    //                                            }

                                    $pdf->SetTextColor(0, 0, 0);

                                    if ($visualizza_assenze == "SI" || $visualizza_assenze == "SENZA_TOTALE") {
                                        $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze), 'TBR', 0, 'C', $fill);
                                        $testo_excel .= str_replace('.', ',', $inserimento_voto_numerico) . "	" . str_replace('.', ',', stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze)) . "	";
                                    } elseif ($visualizza_assenze == "PERCENTUALE") {

                                        if ($elenco_voti_pagelline[$cont]['monteore_totale'] != 0) {
                                            $ass_perc_mat = round($elenco_voti_pagelline[$cont][12] / $elenco_voti_pagelline[$cont]['monteore_totale'], 4) * 100;
                                            $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, $ass_perc_mat . '%', 'TBR', 0, 'C', $fill);
                                            $testo_excel .= "$ass_perc_mat%	";
                                        } else { // monteore 0
                                            $pdf->CellFitScale($dimensione_mezza_cella, $altezza_cella, '0%', 'TBR', 0, 'C', $fill);
                                            $testo_excel .= "0%	";
                                        }
                                    }
                                } else {
                                    $bordo = 'LTBR';
                                    if ($parametro_debiti == "SI") {
                                        $dimensione_mezza_cella_old = $dimensione_mezza_cella;
                                        $dimensione_cella_old = $dimensione_cella;
                                        $dimensione_mezza_cella = $dimensione_cella / 2;
                                        $dimensione_cella = $dimensione_mezza_cella;
                                        $bordo = 'LTB';
                                    }
                                    //                                            if ($trentino_abilitato == 'SI'
                                    //                                                    && $esposizione_archivio == 'esposizione'
                                    //                                                    && in_array(intval($elenco_voti_pagelline[$cont][10]), [1,2,3,4,5])
                                    //                                                    && verifica_presenza_debito($id_studente,$elenco_voti_pagelline[$cont]['id_materia'], $anno_scolastico)) {
                                    //                                                $pdf->SetTextColor(0,0,0);
                                    //                                                $pdf->CellFitScale($dimensione_cella,$altezza_cella,6,'LTBR',0,'C',$fill);
                                    //                                            } elseif ($trentino_abilitato == 'SI'
                                    //                                                    && $esposizione_archivio == 'esposizione'
                                    //                                                    && ($elenco_voti_pagelline[$cont][10])
                                    //                                                    && ($dati_studente['pei'] == 'SI' || $dati_studente['bes'] == 'SI')
                                    //                                                    && (in_array(intval($elenco_voti_pagelline[$cont][10]), [1,2,3,4,5]) || $elenco_voti_pagelline[$cont][10] == '')) {
                                    //                                                $pdf->SetTextColor(0,0,0);
                                    //
                                    //                                                if ($dati_studente['esonero_religione'] == 1 && $elenco_voti_pagelline[$cont]['tipo_materia'] == "RELIGIONE" && $elenco_voti_pagelline[$cont][10] == '') {
                                    //                                                    $pdf->CellFitScale($dimensione_cella,$altezza_cella,'','LTB',0,'C',$fill);
                                    //                                                } else {
                                    //                                                    $pdf->CellFitScale($dimensione_cella,$altezza_cella,6,'LTB',0,'C',$fill);
                                    //                                                }
                                    //                                            } else {
                                    //                                                $pdf->CellFitScale($dimensione_cella,$altezza_cella,$inserimento_voto_numerico, $bordo,0,'C',$fill);
                                    //                                            }
                                    //                                            if($parametro_debiti == "SI")
                                    //                                            {
                                    //                                                $pdf->CellFitScale($dimensione_cella,$altezza_cella,$elenco_voti_pagelline[$cont]['tipo_recupero'],'TBR',0,'C',$fill);
                                    //                                            }
                                    $pdf->CellFitScale($dimensione_cella, $altezza_cella, $elenco_voti_pagelline[$cont]['tipo_recupero_tradotto'], 'TBR', 0, 'C', $fill);

                                    $pdf->SetTextColor(0, 0, 0);
                                    $testo_excel .= str_replace('.', ',', $elenco_voti_pagelline[$cont]['tipo_recupero_tradotto']) . "	";
                                }
                                if ($parametro_debiti == "SI") {
                                    $dimensione_mezza_cella = $dimensione_mezza_cella_old;
                                    $dimensione_cella = $dimensione_cella_old;
                                }
                                //                                    }
                                //                                }
                                //}}} </editor-fold>
                                //setto il margine della cella al valore scelto dall'utente
                                $pdf->cMargin = $valore_margine_cella_voti;
                                //{{{ <editor-fold defaultstate="collapsed" desc="scritto orale pratico e solo scritto e orale">
                                //                                        if($celle_colorate == 'SI')
                                //                                        {
                                //                                            $pdf->SetFillColor(255,255,255);
                                //                                        }
                                //}}} </editor-fold>

                                if ($visualizza_assenze == "SOLO_ASSENZE") {
                                    $pdf->CellFitScale($dimensione_cella, $altezza_cella, stampa_ore_o_minuti($elenco_voti_pagelline[$cont][12], $stampa_tipo_assenze), 'TBRL', 0, 'C', $fill);
                                    $testo_excel .= str_replace('.', ',', $elenco_voti_pagelline[$cont][12]) . "	";
                                    $tot_ore_assenze = $tot_ore_assenze + $elenco_voti_pagelline[$cont][12];
                                }

                                if ($visualizza_assenze == "SOLO_TOTALE" || $visualizza_assenze == "SI") {
                                    $tot_ore_assenze = $tot_ore_assenze + $elenco_voti_pagelline[$cont][12];
                                }

                                if ($visualizza_assenze == "PERCENTUALE") {
                                    if ($elenco_voti_pagelline[$cont]['monteore_totale'] != 0) {
                                        $tot_monteore_assenze += $elenco_voti_pagelline[$cont]['monteore_totale'];
                                        $tot_ore_assenze = $tot_ore_assenze + $elenco_voti_pagelline[$cont][12];
                                    }
                                }

                                $pdf->cMargin = $margine_cella;
                                //}}} </editor-fold>
                            }
                        }
                        //}}} </editor-fold>
                    }

                    //{{{ <editor-fold defaultstate="collapsed" desc="colonne aggiuntive dopo i voti">
                    if ($visualizza_assenze == "SOLO_ASSENZE" || $visualizza_assenze == "SOLO_TOTALE" || $visualizza_assenze == "SI" || $visualizza_assenze == "PERCENTUALE") {

                        if ((($visualizza_bocciati == "NO") && ($passato == "NO")) || (($visualizza_sospesi == "NO") && ($passato == "NI") && (($da_estero != 'SI')))) {
                            $pdf->CellFitScale($dimensione_cella, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= " 	";
                        }
                        else {
                            if ($visualizza_assenze != "PERCENTUALE") {
                                if ($stampa_tipo_assenze == 'ORE_ARROTONDATE') {
                                    $pdf->CellFitScale($dimensione_cella, $altezza_cella, $tot_ore_assenze_colonna_unica_arrotondate, 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale($dimensione_cella, $altezza_cella, stampa_ore_o_minuti($tot_ore_assenze_colonna_unica, $stampa_tipo_assenze), 1, 0, 'C', $fill);
                                    $testo_excel .= str_replace('.', ',', $tot_ore_assenze_colonna_unica) . "	";
                                }
                            }
                            else {

                                if ($tot_monteore_assenze == 0) {
                                    $ass_perc_tot = 0;
                                } else {
                                    $ass_perc_tot = round($tot_ore_assenze_colonna_unica / $tot_monteore_assenze, 4) * 100;
                                }

                                $pdf->CellFitScale($dimensione_cella, $altezza_cella, $ass_perc_tot . '%', 1, 0, 'C', $fill);
                                $testo_excel .= str_replace('.', ',', $ass_perc_tot . '%') . "	";
                            }
                        }
                    }

                    if ($tot_voti_pagella > 0) {
                        //    echo_debug($somma_voti_pagella . "<br>");
                        $media_voti_pagella = round(($somma_voti_pagella / $tot_voti_pagella), 2);
                    } else {
                        $media_voti_pagella = 0;
                    }

                    if ($stampa_medie == 'SI' || $stampa_medie == "SI_TOT") {
                        //{{{ <editor-fold defaultstate="collapsed">
                        $nuova_media = str_replace('.', ',', $media_voti_pagella);

                        if (($visualizza_bocciati == "NO") && ($passato == "NO") && $carenze_trentino == false && ($da_estero != 'SI')) {
                            $pdf->Cell($dimensione_cella, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } elseif (($visualizza_sospesi == 'NO' && $passato == 'NI') && $carenze_trentino == false && ($da_estero != 'SI')) {
                            $pdf->Cell($dimensione_cella, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } elseif ($visualizza_ammesso_con_insufficienze == 'NO' && strpos(strtoupper($esito['esito']), 'AMMESSO SECONDO O.M.') !== false && $tot_insuff > 0 && ($da_estero != 'SI')) {
                            $pdf->Cell($dimensione_cella, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } else {
                            if (strlen($media_voti_pagella) > 0) {
                                $pdf->CellFitScale($dimensione_cella, $altezza_cella, $media_voti_pagella . ' ', 1, 0, 'C', $fill);
                                $testo_excel .= $nuova_media . "	";
                            } else {
                                $pdf->Cell($dimensione_cella, $altezza_cella, $media_voti_pagella . ' ', 1, 0, 'C', $fill);
                                $testo_excel .= $nuova_media . "	";
                            }

                            $sum_voti_classe += $media_voti_pagella; // calc dentro qui? penso si
                            $cont_stud_voti_classe++; // calc dentro qui? penso si
                        }
                        //}}} </editor-fold>
                    }

                    if ($stampa_crediti_integrativi != 'NO') {
                        // Anno Corrente
                        $crediti_terza = intval($dati_studente['crediti_terza']);
                        $crediti_quarta = intval($dati_studente['crediti_quarta']);
                        $crediti_quinta = intval($dati_studente['crediti_quinta']);

                        $crediti_integrati_terza = intval($dati_studente["crediti_reintegrati_terza"]);
                        $crediti_integrati_quarta = intval($dati_studente["crediti_reintegrati_quarta"]);
                        $crediti_integrati_quinta = intval($dati_studente["crediti_finali_agg"]);

                        $tot_crediti_integrativi = $crediti_integrati_terza + $crediti_integrati_quarta + $crediti_integrati_quinta;

                        $crediti_totali = $crediti_terza + $crediti_quarta + $crediti_quinta + $tot_crediti_integrativi;
                    } else {
                        $crediti_terza = intval($dati_studente['crediti_terza']) + intval($dati_studente["crediti_reintegrati_terza"]);
                        $crediti_quarta = intval($dati_studente['crediti_quarta']) + intval($dati_studente["crediti_reintegrati_quarta"]);
                        $crediti_quinta = intval($dati_studente['crediti_quinta']) + intval($dati_studente["crediti_finali_agg"]);

                        $crediti_totali = $crediti_terza + $crediti_quarta + $crediti_quinta;
                    }

                    $crediti_totali_om_2022 = converti_crediti_om_2022($crediti_totali);

                    if (($dati_studente['classe'] == 3 && $dati_studente['tipo_indirizzo'] != 1 && $dati_studente['tipo_indirizzo'] != 5) || ($dati_studente['tipo_indirizzo'] == 1 && $dati_studente['classe'] == 1) || ($dati_studente['tipo_indirizzo'] == 5 && $dati_studente['classe'] == 2)
                    ) {
                        $crediti_anno_corrente = $crediti_terza;
                        $crediti_integrati_anno_corrente = $crediti_integrati_terza;
                    } elseif (($dati_studente['classe'] == 4 && $dati_studente['tipo_indirizzo'] != 1 && $dati_studente['tipo_indirizzo'] != 5) || ($dati_studente['tipo_indirizzo'] == 1 && $dati_studente['classe'] == 2) || ($dati_studente['tipo_indirizzo'] == 5 && $dati_studente['classe'] == 3)
                    ) {
                        $crediti_anno_corrente = $crediti_quarta;
                        $crediti_integrati_anno_corrente = $crediti_integrati_quarta;
                    } elseif (($dati_studente['classe'] == 5 && $dati_studente['tipo_indirizzo'] != 1 && $dati_studente['tipo_indirizzo'] != 5) || ($dati_studente['tipo_indirizzo'] == 1 && $dati_studente['classe'] == 3) || ($dati_studente['tipo_indirizzo'] == 5 && $dati_studente['classe'] == 4)
                    ) {
                        $crediti_anno_corrente = $crediti_quinta;
                        $crediti_integrati_anno_corrente = $crediti_integrati_quinta;
                    }
                    // se stampo i crediti integrativi totali sovrascrivo i crediti integrati anno corrente
                    $crediti_integrati_anno_corrente = $stampa_crediti_integrativi == 'TOT' ? $tot_crediti_integrativi : $crediti_integrati_anno_corrente;

                    if ($stampa_medie_provenienza == 'SI' && $abilita_stampa_medie_provenienza = 'SI') {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Fra 04/01/13 stampo la scuola media di provenienza">
                        if (($visualizza_bocciati == "NO") && ($passato == "NO")) {
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "			";
                        } else {
                            $pdf->SetFont('helvetica', 'B', 8);
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, $scuola_media_provenienza, 1, 0, 'C', $fill);
                            $testo_excel .= $scuola_media_provenienza . "	";
                        }
                        //}}} </editor-fold>
                    }

                    $pdf->SetFont('helvetica', 'B', $dimensione_font);

                    if ($stampa_crediti == "SI") {
                        // Abilito la visualizzazione dei crediti per i frequentanti all'estero
                        if ($controllo_voto_singolo == 'Frequenza anno estero') {
                            $passato = 'SI';
                        }

                        //{{{ <editor-fold defaultstate="collapsed">
                        if ($visualizza_bocciati == "NO" && $passato == "NO" && $carenze_trentino == false && ($da_estero != 'SI')) {
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "			";
                        } elseif ($visualizza_sospesi == "NO" && $passato == "NI" && $carenze_trentino == false) {
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "			";
                        } else {
                            // Se lo studente ha giudizio sospeso ma ha crediti stampa comunque 0 crediti per l'anno in corso
                            if ($passato == "NI") {
                                $crediti_terza = $dati_studente['anno_reale_classe'] == 3 ? '0' : $crediti_terza;
                                $crediti_quarta = $dati_studente['anno_reale_classe'] == 4 ? '0' : $crediti_quarta;
                            }
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, $crediti_terza, 1, 0, 'C', $fill);
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, $crediti_quarta, 1, 0, 'C', $fill);
                            $pdf->CellFitScale($dimensione_cella * 2 / 3, $altezza_cella, $crediti_quinta, 1, 0, 'C', $fill);
                            $testo_excel .= $crediti_terza . "	" . $crediti_quarta . "	" . $crediti_quinta . "	";
                        }
                        //}}} </editor-fold>
                    }

                    if ($stampa_crediti_anno_corrente == 'SI') {
                        //{{{ <editor-fold defaultstate="collapsed">
                        if (($visualizza_bocciati == "NO") && ($passato == "NO") && $carenze_trentino == false && ($da_estero != 'SI')) {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } elseif (($visualizza_sospesi == "NO") && ($passato == "NI") && $carenze_trentino == false) {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } else {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, $crediti_anno_corrente, 1, 0, 'C', $fill);
                            $testo_excel .= $crediti_anno_corrente . "	";
                        }
                        //}}} </editor-fold>
                    }

                    if ($stampa_crediti_integrativi != 'NO') {
                        if (($visualizza_bocciati == "NO") && ($passato == "NO") && $carenze_trentino == false && ($da_estero != 'SI')) {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } elseif (($visualizza_sospesi == "NO") && ($passato == "NI") && $carenze_trentino == false) {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } else {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, $crediti_integrati_anno_corrente, 1, 0, 'C', $fill);
                            $testo_excel .= $crediti_integrati_anno_corrente . "	";
                        }
                    }

                    if ($stampa_crediti_totali == "SI") {
                        //{{{ <editor-fold defaultstate="collapsed">
                        if (($visualizza_bocciati == "NO") && ($passato == "NO") && $carenze_trentino == false && ($da_estero != 'SI')) {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } elseif (($visualizza_sospesi == "NO") && ($passato == "NI") && $carenze_trentino == false) {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } else {
                            $pdf->CellFitScale($dimensione_cella / 2, $altezza_cella, $crediti_totali, 1, 0, 'C', $fill);
                            $testo_excel .= $crediti_totali . "	";
                        }
                        //}}} </editor-fold>
                    }

                    //                    if($stampa_crediti_totali_om_2022 == "SI")
                    //                    {
                    //                        //{{{ <editor-fold defaultstate="collapsed">
                    //                        if(($visualizza_bocciati == "NO") && ($passato == "NO") && $carenze_trentino == false && ($da_estero != 'SI'))
                    //                        {
                    //                            $pdf->CellFitScale($dimensione_cella/2, $altezza_cella, '', 1, 0, 'C', $fill);
                    //                            $testo_excel .= "    ";
                    //                        }
                    //                        elseif(($visualizza_sospesi == "NO") && ($passato == "NI") && $carenze_trentino == false)
                    //                        {
                    //                            $pdf->CellFitScale($dimensione_cella/2, $altezza_cella, '', 1, 0, 'C', $fill);
                    //                            $testo_excel .= "    ";
                    //                        }
                    //                        else
                    //                        {
                    //                            $pdf->CellFitScale($dimensione_cella/2, $altezza_cella, $crediti_totali_om_2022, 1, 0, 'C', $fill);
                    //                            $testo_excel .= $crediti_totali_om_2022 . "    ";
                    //                        }
                    //                        //}}} </editor-fold>
                    //                    }

                    if ($stampa_risultato == "SI_CON_VALORI") {
                        //{{{ <editor-fold defaultstate="collapsed">

                        $dim_calc_ris = 3;
                        // ammissione 3 medie
                        if ($dati_classe['tipo_indirizzo'] == 4 && $dati_classe['classe'] == 3
                        ) {
                            if ($stringa_anno_scolastico == '2019/2020') {
                                $dim_calc_ris = 3;
                            } else {
                                $dim_calc_ris = 2.3;
                            }
                        }

                        if ($ammesso_con_debito == "SI" && strlen($elenco_debiti_comma_4a[0]) > 0) {
                            $controllo_voto_singolo = $controllo_voto_singolo . " con carenze";
                            // CONVERSIONE IN BASE AL SESSO AMMESSO/A
                            $controllo_voto_singolo = str_replace('messo', "mess$suffisso_oa", $controllo_voto_singolo);
                            $controllo_voto_singolo = str_replace('MESSO', "MESS$suffisso_OA", $controllo_voto_singolo);
                        }

                        if ($visualizza_nomi_bocciati == "NO" && $passato == "NO") {
                            $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } elseif ($visualizza_nomi_sospesi == "NO" && $passato == "NI") {
                            $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, '', 1, 0, 'C', $fill);
                            $testo_excel .= "	";
                        } elseif ($trentino_abilitato == 'SI' && $carenze_trentino == true && $esposizione_archivio == 'esposizione'
                        ) {
                            if ($esito_manuale_superiori == 'SI') {
                                $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, $controllo_voto_singolo, 1, 0, 'C', $fill);
                            } else {
                                $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, 'Ammess' . $suffisso_oa . ' alla classe successiva*', 1, 0, 'C', $fill);
                            }
                            $testo_excel .= $controllo_voto_singolo . "      ";
                        } elseif ($trentino_abilitato == 'SI' && ($dati_studente['pei'] == 'SI' || $dati_studente['bes'] == 'SI') && $esposizione_archivio == 'esposizione' && $esito['scrutinato'] == true
                        ) {
                            if ($dati_studente['classe'] == 5) {
                                if ($esito_manuale_superiori == 'SI') {
                                    $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, $controllo_voto_singolo, 1, 0, 'C', $fill);
                                } else {
                                    $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, 'Ammess' . $suffisso_oa . ' esame di stato*', 1, 0, 'C', $fill);
                                }
                            } else {
                                if ($attiva_stampa_pei == 'SI' || $carenze_trentino == true) {
                                    if ($esito_manuale_superiori == 'SI') {
                                        $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, $controllo_voto_singolo, 1, 0, 'C', $fill);
                                    } else {
                                        $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, 'Ammess' . $suffisso_oa . ' alla classe successiva*', 1, 0, 'C', $fill);
                                    }
                                } else {
                                    if ($esito_manuale_superiori == 'SI') {
                                        $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, $controllo_voto_singolo, 1, 0, 'C', $fill);
                                    } else {
                                        $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, 'Ammess' . $suffisso_oa . ' alla classe successiva', 1, 0, 'C', $fill);
                                    }
                                }
                            }

                            $testo_excel .= $controllo_voto_singolo . "\t";
                        } else {
                            $pdf->CellFitScale($dimensione_cella * $dim_calc_ris, $altezza_cella, $controllo_voto_singolo, 1, 0, 'C', $fill);
                            $testo_excel .= $controllo_voto_singolo . "\t";
                        }

                        // voto ammissione esame terza media
                        if ($dati_classe['tipo_indirizzo'] == 4 && $dati_classe['classe'] == 3
                        ) {
                            if ($stringa_anno_scolastico == '2019/2020') {

                            } else {
                                $pdf->CellFitScale($dimensione_cella * (3 - $dim_calc_ris), $altezza_cella, $dati_studente['voto_ammissione_medie'], 1, 0, 'C', $fill);
                            }
                            $testo_excel .= $dati_studente['voto_ammissione_medie'] . "\t";
                        }

                        //}}} </editor-fold>
                    } elseif ($stampa_risultato == "SI_SENZA_VALORI") {
                        $pdf->CellFitScale($dimensione_cella * 3, $altezza_cella, '', 1, 0, 'C', $fill);
                        $testo_excel .= "	";
                    }

                    if ($stampa_annotazioni == "SI") {
                        $pdf->Cell($dimensione_cella * 2, $altezza_cella, '', 1, 0, 'C', $fill);
                    }

                    //Campi Multicell per debiti Trentino
                    if ($stampa_debiti_4a == "SI" && $stampa_debiti_4b != "SI") {
                        //{{{ <editor-fold defaultstate="collapsed">
                        if (($visualizza_bocciati == "NO") && ($passato == "NO")) {
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 1, 'C', $fill);
                        } else {
                            $cont_fin_deb = 0;

                            for ($cont_deb = 0; $cont_deb < count($elenco_debiti_comma_4a); $cont_deb++) {
                                if ($elenco_debiti_comma_4a[$cont_deb] != "") {
                                    $cont_fin_deb++;
                                }
                            }

                            $pdf->SetFont('helvetica', 'B', 6);
                            $stringa_debiti_4a = substr($stringa_debiti_4a, 0, -2);
                            $pdf->MultiCell($dimensione_cella * 3, $altezza_cella, $stringa_debiti_4a, 1, 'L', $fill, 1, '', '', true, 0, false, true, $altezza_cella, 'T', true);
                        }

                        $pdf->SetFont('helvetica', 'B', $dimensione_font);
                        $testo_excel .= "	";

                        foreach ($elenco_debiti_comma_4a as $debito) {
                            $testo_excel .= $debito . ' ';
                        }

                        //                        $testo_excel .= "        ";
                        //}}} </editor-fold>
                    }

                    if ($stampa_debiti_4a != "SI" && $stampa_debiti_4b == "SI") {
                        //{{{ <editor-fold defaultstate="collapsed">
                        if (($visualizza_bocciati == "NO") && ($passato == "NO")) {
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 1, 'C', $fill);
                        } elseif (($visualizza_sospesi == "NO") && ($passato == "NI")) {
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 1, 'C', $fill);
                        } else {
                            $cont_fin_deb = 0;

                            for ($cont_deb = 0; $cont_deb < count($elenco_debiti_comma_4b); $cont_deb++) {
                                if ($elenco_debiti_comma_4b[$cont_deb] != "") {
                                    $cont_fin_deb++;
                                }
                            }

                            $pdf->MultiCell($dimensione_cella * 3, $altezza_cella, $stringa_debiti_4b, 1, 'L', $fill, 1, '', '', true, 0, false, true, $altezza_cella, 'T', true);
                        }

                        $pdf->SetFont('helvetica', 'B', $dimensione_font);
                        $testo_excel .= $elenco_debiti_comma_4b[0] . $elenco_debiti_comma_4b[1] . $elenco_debiti_comma_4b[2] . $elenco_debiti_comma_4b[3] . $elenco_debiti_comma_4b[4] . "        ";
                        //}}} </editor-fold>
                    }

                    if ($stampa_debiti_4a == "SI" && $stampa_debiti_4b == "SI") {
                        //{{{ <editor-fold defaultstate="collapsed">
                        if (($visualizza_bocciati == "NO") && ($passato == "NO")) {
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 1, 'C', $fill);
                        } elseif (($visualizza_sospesi == "NO") && ($passato == "NI")) {
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 0, 'C', $fill);
                            $pdf->Cell($dimensione_cella * 3, $altezza_cella, '', 1, 1, 'C', $fill);
                        } else {
                            $pdf->SetFont('helvetica', 'B', 6);
                            $pdf->MultiCell($dimensione_cella * 3, $altezza_cella, $stringa_debiti_4a, 1, 'L', $fill, 0, '', '', true, 0, false, true, $altezza_cella, 'T', true);
                            $pdf->MultiCell($dimensione_cella * 3, $altezza_cella, $stringa_debiti_4b, 1, 'L', $fill, 1, '', '', true, 0, false, true, $altezza_cella, 'T', true);
                        }

                        $pdf->SetFont('helvetica', 'B', $dimensione_font);
                        $testo_excel .= "	";

                        foreach ($elenco_debiti_comma_4a as $debito) {
                            $testo_excel .= $debito . ' ';
                        }

                        //                        $testo_excel .= "        ";
                        //}}} </editor-fold>
                    }

                    if ($stampa_debiti_4a == "NO" && $stampa_debiti_4b == "NO") {
                        $pdf->Cell(0, $altezza_cella, '', 0, 1, 'C');
                    }

                    $testo_excel .= chr(13) . chr(10);
                    //}}} </editor-fold>
                    //}}} </editor-fold>
                }
                //}}} </editor-fold>
            }
        }
        //}}} </editor-fold>
    }
}

// stampa della mediadella classe (media delle medie)
if ($stampa_medie == 'SI_TOT') {
    $media_classe = round(($sum_voti_classe / $cont_stud_voti_classe), 2);

    //    $pdf->SetX($x_col_media);
    //    $pdf->CellFitScale($dimensione_cella, $altezza_cella, $media_classe, 1, 1, 'C');

    $pdf->Ln(1);
    $pdf->SetFont('helvetica', 'I', $dimensione_font);
    $pdf->Cell(0, 0, 'Media della classe: ' . $media_classe . '.', 0, 0, 'L');

    $pdf->Ln(3);
}

$pdf->SetFont('helvetica', 'B', 7);

if ($stampa_pei == 'SI' && !$dsa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="stampa descrizione PEI">
    $commento_pei = "La presenza di un asterisco prima del cognome indica che l'alunno/a è stato/a valutato/a sulla base "
            . "del P.E.I. (Piano Educativo Individualizzato) adottato, conforme all'O.M. n°90 del 2001 art. 15.";
    $pdf->ln(2);
    $pdf->SetFont('helvetica', '', 8);
    $pdf->CellFitScale(0, 3, $commento_pei, 0, 1, 'L');
    //}}} </editor-fold>
} elseif ($stampa_pei == 'SI' && $dsa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="stampa descrizione DSA">
    $commento_dsa = "La presenza di due asterischi prima del cognome indica che l'alunno/a è stato/a valutato/a ai sensi della legge n°170 del 08 ottobre 2010.";
    $pdf->ln(2);
    $pdf->SetFont('helvetica', '', 8);
    $pdf->CellFitScale(0, 3, $commento_dsa, 0, 1, 'L');
    //}}} </editor-fold>
}

if ($stampa_firme == 'SI') {
    //{{{ <editor-fold defaultstate="collapsed">

    $y_base = $pdf->GetY() + 3;
    $pdf->SetXY(10, $y_base);
    $elenchi_prof = [];

    if ($multi_classe_pagelle == 'SI') {
        $elenco_classi = estrai_multi_classi_da_classe((int) $id_classe);
    } else {
        $elenco_classi[0]['id_classe'] = $id_classe;
    }

    foreach ($elenco_classi as $classe) {
        $prof_classe = estrai_abbinamenti((int) $classe['id_classe'], 'classe');
        $arr_prof = [];
        foreach ($prof_classe as $professore_singolo) {
            if ($professore_singolo['tipo_materia'] != 'SOSTEGNO') {
                $arr_prof[] = $professore_singolo;
            }
        }
        $elenchi_prof[] = $arr_prof;
    }
    $elenchi_prof[] = estrai_docenti_sostegno_classe($id_classe);
    $elenco_finale_prof = [];
    $cont_prof_int = 0;

    if (is_array($elenchi_prof)) {
        foreach ($elenchi_prof as $elenco_prof) {
            if (is_array($elenco_prof)) {
                foreach ($elenco_prof as $prof_singolo) {
                    $esiste_prof = 'NO';
                    if ($prof_singolo['tipo_materia'] == 'SOSTEGNO') {
                        foreach ($elenco_finale_prof as $elemento) {
                            if ($elemento['id_professore'] == $prof_singolo['id_professore']) {
                                $esiste_prof = 'SI';
                            }
                        }
                    }

                    if ($esiste_prof == 'NO') {
                        $sostituzione = sostituisci_docente_consiglio($id_classe, $periodo, $prof_singolo['id_professore']);

                        $elenco_finale_prof[$cont_prof_int]['id_classe'] = $prof_singolo['id_classe'];
                        $elenco_finale_prof[$cont_prof_int]['id_professore'] = $prof_singolo['id_professore'];
                        $elenco_finale_prof[$cont_prof_int]['cognome'] = $prof_singolo['cognome'];
                        $elenco_finale_prof[$cont_prof_int]['nome'] = $prof_singolo['nome'];
                        $elenco_finale_prof[$cont_prof_int]['tipo_materia'] = $prof_singolo['tipo_materia'];
                        $elenco_finale_prof[$cont_prof_int]['in_media_pagelle'] = $prof_singolo['in_media_pagelle'];
                        $elenco_finale_prof[$cont_prof_int]['presenza'] = $sostituzione['stato'];

                        if ($sostituzione['stato'] == 'S') {
                            $elenco_finale_prof[$cont_prof_int]['cognome_sostituto'] = $sostituzione['cognome'];
                            $elenco_finale_prof[$cont_prof_int]['nome_sostituto'] = $sostituzione['nome'];
                        }
                        $cont_prof_int++;
                    }
                }
            }
        }
    }

    $cont_prof = 0;
    $pdf->Cell(0, 4, '', 0, 1, 'C');
    $x_base_tmp = $pdf->GetX();
    $y_base_tmp = $pdf->GetY();
    $pdf->SetXY($x_base_tmp, $y_base_tmp);

    foreach ($elenco_finale_prof as $prof) {
        $nome_sost = $prof['presenza'] == 'S' ? "(Sost. " . decode($prof['cognome_sostituto']) . " " . decode($prof['nome_sostituto']) . ")" : "";
        $nome_sost = $prof['presenza'] == 'A' ? "(Assente)" : $nome_sost;

        $nome_prof = $prof['cognome'] . ' ' . $prof['nome'];
        $prossima_larghezza_raggiunta = (($cont_prof + 1) * 35);

        if ($prossima_larghezza_raggiunta > ($larghezza_pagina - 20)) {
            $y_base = $pdf->GetY() + 11;
            $pdf->SetXY(10, $y_base);
            $cont_prof = 0;
        }

        if ($prof['tipo_materia'] != 'CONDOTTA' && ($prof['in_media_pagelle'] != 'NV' || $param_tabellone_firme_materie_NV == 'SI')) {
            $cont_prof++;
            $x_base = $pdf->GetX();
            $y_base = $pdf->GetY();
            $pdf->CellFitScale(33, 2, '__________________________________', 0, 0, 'C');
            $x_finale = $pdf->GetX();
            $y_finale = $pdf->GetY();
            $pdf->SetXY($x_base, $y_base + 3);
            $pdf->CellFitScale(33, 4, $nome_prof, 0, 0, 'C');
            $pdf->CellFitScale(2, 4, '', 0, 0, 'C');
            //            if ($stampa_presenze == 'SI') {
            $pdf->SetXY($x_base, $y_base + 6);
            $pdf->CellFitScale(33, 4, $nome_sost, 0, 0, 'C');
            $pdf->CellFitScale(2, 4, '', 0, 0, 'C');
            //            }
            $pdf->SetXY($x_finale, $y_finale);
            $testo_excel .= $nome_prof . '	';
        }
    }

    $pdf->Cell(0, 6, '', 0, 1, 'C');
    $testo_excel .= chr(13) . chr(10);
    //}}} </editor-fold>
}

if ($stampa_firme == 'SI_FIRME_SINGOLE') {
    //{{{ <editor-fold defaultstate="collapsed">
    $y_base = $pdf->GetY() + 3;
    $pdf->SetXY(10, $y_base);
    $elenchi_prof = [];
    $elenco_classi = [];

    if ($multi_classe_pagelle == 'SI') {
        $elenco_classi = estrai_multi_classi_da_classe((int) $id_classe);
    } else {
        $elenco_classi[0]['id_classe'] = $id_classe;
    }

    foreach ($elenco_classi as $classe) {
        $prof_classe = estrai_abbinamenti((int) $classe['id_classe'], 'classe');
        $arr_prof = [];
        foreach ($prof_classe as $professore_singolo) {
            if ($professore_singolo['tipo_materia'] != 'SOSTEGNO') {
                $arr_prof[] = $professore_singolo;
            }
        }
        $elenchi_prof[] = $arr_prof;
    }

    $elenchi_prof[] = estrai_docenti_sostegno_classe($id_classe);
    $elenco_finale_prof = [];
    $cont_prof_int = 0;

    foreach ($elenchi_prof as $elenco_prof) {
        foreach ($elenco_prof as $prof_singolo) {
            if ($prof_singolo['tipo_materia'] != 'CONDOTTA') {
                $esiste_prof = 'NO';

                foreach ($elenco_finale_prof as $elemento) {
                    if ($elemento['id_professore'] == $prof_singolo['id_professore']) {
                        $esiste_prof = 'SI';
                    }
                }

                if ($esiste_prof == 'NO' && ($prof_singolo['in_media_pagelle'] != 'NV' || $param_tabellone_firme_materie_NV == 'SI')) {
                    $sostituzione = sostituisci_docente_consiglio($id_classe, $periodo, $prof_singolo['id_professore']);

                    $elenco_finale_prof[$cont_prof_int]['id_classe'] = $prof_singolo['id_classe'];
                    $elenco_finale_prof[$cont_prof_int]['id_professore'] = $prof_singolo['id_professore'];
                    $elenco_finale_prof[$cont_prof_int]['cognome'] = $prof_singolo['cognome'];
                    $elenco_finale_prof[$cont_prof_int]['nome'] = $prof_singolo['nome'];
                    $elenco_finale_prof[$cont_prof_int]['in_media_pagelle'] = $prof_singolo['in_media_pagelle'];
                    $elenco_finale_prof[$cont_prof_int]['presenza'] = $sostituzione['stato'];

                    if ($sostituzione['stato'] == 'S') {
                        $elenco_finale_prof[$cont_prof_int]['cognome_sostituto'] = $sostituzione['cognome'];
                        $elenco_finale_prof[$cont_prof_int]['nome_sostituto'] = $sostituzione['nome'];
                    }
                    $cont_prof_int++;
                }
            }
        }
    }

    file_put_contents('/tmp/finale', print_r($elenco_finale_prof, true));

    $cont_prof = 0;
    $pdf->Cell(0, 2, '', 0, 1, 'C');
    $x_base_tmp = $pdf->GetX();
    $y_base_tmp = $pdf->GetY();
    $pdf->SetXY($x_base_tmp, $y_base_tmp);

    foreach ($elenco_finale_prof as $prof) {
        $nome_sost = $prof['presenza'] == 'S' ? "(Sost. " . decode($prof['cognome_sostituto']) . " " . decode($prof['nome_sostituto']) . ")" : "";
        $nome_sost = $prof['presenza'] == 'A' ? "(Assente)" : $nome_sost;

        $nome_prof = $prof['cognome'] . ' ' . $prof['nome'];
        $prossima_larghezza_raggiunta = (($cont_prof + 1) * 35);

        if ($prossima_larghezza_raggiunta > ($larghezza_pagina - 20)) {
            $y_base = $pdf->GetY() + 11;
            $pdf->SetXY(10, $y_base);
            $cont_prof = 0;
        }

        $cont_prof++;
        $x_base = $pdf->GetX();
        $y_base = $pdf->GetY();
        $pdf->CellFitScale(33, 2, '__________________________________', 0, 0, 'C');
        $x_finale = $pdf->GetX();
        $y_finale = $pdf->GetY();
        $pdf->SetXY($x_base, $y_base + 3);
        $pdf->CellFitScale(33, 4, $nome_prof, 0, 0, 'C');
        $pdf->CellFitScale(2, 4, '', 0, 0, 'C');
        if ($stampa_presenze == 'SI') {
            $pdf->SetXY($x_base, $y_base + 6);
            $pdf->CellFitScale(33, 4, $nome_sost, 0, 0, 'C');
            $pdf->CellFitScale(2, 4, '', 0, 0, 'C');
        }
        $pdf->SetXY($x_finale, $y_finale);
        $testo_excel .= $nome_prof . '	';
    }

    $pdf->Cell(0, 3, '', 0, 1, 'C');
    $testo_excel .= chr(13) . chr(10);
    //}}} </editor-fold>
}

if (($stampo_debiti_sui_voti != 'NO') && ( ( intval($dati_classe[2]) != 5 && $dati_classe['tipo_indirizzo'] != '1' && $dati_classe['tipo_indirizzo'] != '5' ) || ( intval($dati_classe[2]) != 3 && $dati_classe['tipo_indirizzo'] == '1' ) || ( intval($dati_classe[2]) != 4 && $dati_classe['tipo_indirizzo'] == '5' ) )
) {
    //{{{ <editor-fold defaultstate="collapsed" desc="stampa legenda">
    if (($cont_tot_comma4a > 0) && ($cont_tot_comma4b > 0)) {
        $pdf->ln(2);
        $pdf->SetFont('helvetica', '', 8);
        $pdf->Cell(15, 4, 'Legenda: ', 0, 0, 'L');
        $pdf->Cell(0, 4, 'Le materie contrassegnate da * indicano la presenza del debito', 0, 1, 'L');
        $pdf->Cell(15, 4, '', 0, 0, 'L');
        $pdf->Cell(0, 4, 'Le materie contrassegnate da ** indicano la presenza del debito con recupero autonomo', 0, 1, 'L');
    } else {
        if ($cont_tot_comma4a > 0) {
            $pdf->ln(2);
            $pdf->SetFont('helvetica', '', 8);
            $pdf->Cell(15, 4, 'Legenda: ', 0, 0, 'L');
            $pdf->Cell(0, 4, 'Le materie contrassegnate da * indicano la presenza del debito con la necessità di recupero', 0, 1, 'L');
        }

        if ($cont_tot_comma4b > 0) {
            $pdf->ln(2);
            $pdf->SetFont('helvetica', '', 8);
            $pdf->Cell(15, 4, 'Legenda: ', 0, 0, 'L');
            $pdf->Cell(0, 4, 'Le materie contrassegnate da ** indicano la presenza del debito con recupero autonomo', 0, 1, 'L');
        }
    }
    //}}} </editor-fold>
}

if ($trentino_abilitato == 'SI' and intval($dati_classe[2]) != 5 and $dati_classe['tipo_indirizzo'] != '4' and $dati_classe['tipo_indirizzo'] != '6' and $stampo_debiti_sui_voti == 'SI') {
    $pdf->ln(2);
    $pdf->SetFont('helvetica', '', 8);
    $pdf->Cell(15, 4, 'Legenda: ', 0, 0, 'L');
    $pdf->Cell(0, 4, "(*)  *ai sensi dell'articolo 8 comma c DPP n.22, del 7 ottobre 2010", 0, 1, 'L');
}

//{{{ <editor-fold defaultstate="collapsed" desc="stampa data e firme preside e coordinatore">
if ($visualizza_data == "SI_D") {
    $data_selezionata_finale = mktime(1, 1, 1, intval($data_finale_Month), intval($data_finale_Day), intval($data_finale_Year));
    $y_base = $pdf->GetY() + 5;
    $pdf->SetXY(10, $y_base);
    //$pdf->ln(3);
    $pdf->SetFont('helvetica', 'B', 8);
    $pdf->CellFitScale($larghezza_pagina - 100, 4, $citta . ' lì ' . date("d/m/Y", $data_selezionata_finale), 0, 0, 'L');
    $testo_excel .= $citta . ' lì ' . date("d/m/Y", $data_selezionata_finale) . chr(13) . chr(10);
} elseif ($visualizza_data == "SI_D_DS") {
    $data_selezionata_finale = mktime(1, 1, 1, intval($data_finale_Month), intval($data_finale_Day), intval($data_finale_Year));
    $y_base = $pdf->GetY() + 5;
    $pdf->SetXY(10, $y_base);
    //$pdf->ln(3);
    $pdf->SetFont('helvetica', 'B', 8);
    $pdf->CellFitScale($larghezza_pagina - 100, 4, $citta . ' lì ' . date("d/m/Y", $data_selezionata_finale), 0, 0, 'L');
    $pdf->CellFitScale(0, 5, $definizione_dirigente, 0, 1, 'C');
    $pdf->CellFitScale($larghezza_pagina - 100, 4, '', 0, 0, 'L');
    $pdf->CellFitScale(0, 5, $dirigente_scolastico, 0, 0, 'C');
    if ($param_pagellina_firma_digitale == 'SI') {
        $pdf->ln();
        $pdf->SetFont('helvetica', 'I', 7);
        $pdf->CellFitScale($larghezza_pagina - 100, 4, '', 0, 0, 'L');
        $pdf->CellFitScale(0, 5, "(Documento firmato digitalmente)", 0, 0, 'C');
        $pdf->SetFont('helvetica', 'B', 8);
    }
    if ($param_pagellina_firma_omessa == 'SI') {
        $pdf->ln();
        $pdf->SetFont('helvetica', 'I', 7);
        $pdf->CellFitScale($larghezza_pagina - 100, 4, '', 0, 0, 'L');
        $pdf->CellFitScale(0, 5, 'Firma autografa omessa ai sensi dell’art. 3 del D. Lgs. n. 39/1993', 0, 1, 'C');
        $pdf->SetFont('helvetica', 'B', 8);

        $testo_excel .= chr(13) . chr(10) . 'Firma autografa omessa ai sensi dell’art. 3 del D. Lgs. n. 39/1993';
    }
    $testo_excel .= $definizione_dirigente . chr(13) . chr(10) .
            $dirigente_scolastico . chr(13) . chr(10) .
            $citta . ' lì ' . date("d/m/Y", $data_selezionata_finale) . chr(13) . chr(10);
} elseif ($visualizza_data == "SI_D_DS_C") {
    $coordinatore = estrai_coordinatore((int) $id_classe);
    $data_selezionata_finale = mktime(1, 1, 1, intval($data_finale_Month), intval($data_finale_Day), intval($data_finale_Year));
    $y_base = $pdf->GetY() + 5;
    $pdf->SetXY(10, $y_base);
    //$pdf->ln(3);
    $pdf->SetFont('helvetica', 'B', 8);
    $pdf->CellFitScale($larghezza_pagina / 3, 4, '', 0, 0, 'L');
    $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 4, $definizione_dirigente, 0, 0, 'C');
    $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 4, 'Il Coordinatore di Classe', 0, 1, 'C');
    $pdf->CellFitScale($larghezza_pagina / 3, 2, $citta . ' lì ' . date("d/m/Y", $data_selezionata_finale), 0, 0, 'L');
    $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 2, '_____________________________', 0, 0, 'C');
    $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 2, '_____________________________', 0, 1, 'C');
    $pdf->CellFitScale($larghezza_pagina / 3, 4, '', 0, 0, 'L');
    $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 4, $dirigente_scolastico, 0, 0, 'C');
    $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 4, "Prof. " . $coordinatore["cognome"] . " " . $coordinatore["nome"], 0, 0, 'C');
    if ($param_pagellina_firma_digitale == 'SI') {
        $pdf->ln();
        $pdf->SetFont('helvetica', 'I', 7);
        $pdf->CellFitScale($larghezza_pagina / 3, 4, '', 0, 0, 'L');
        $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 4, "(Documento firmato digitalmente)", 0, 0, 'C');
        $pdf->SetFont('helvetica', 'B', 8);
    }
    if ($param_pagellina_firma_omessa == 'SI') {
        $pdf->ln();
        $pdf->SetFont('helvetica', 'I', 7);
        $pdf->CellFitScale($larghezza_pagina / 3, 4, '', 0, 0, 'L');
        $pdf->CellFitScale(($larghezza_pagina - 20) / 3, 4, 'Firma autografa omessa ai sensi dell’art. 3 del D. Lgs. n. 39/1993', 0, 1, 'C');
        $pdf->SetFont('helvetica', 'B', 8);

        $testo_excel .= chr(13) . chr(10) . 'Firma autografa omessa ai sensi dell’art. 3 del D. Lgs. n. 39/1993';
    }
    $testo_excel .= $definizione_dirigente . '	Il Coordinatore di Classe' . chr(13) . chr(10) .
            $dirigente_scolastico . '	Prof. ' . $coordinatore["cognome"] . " " . $coordinatore["nome"] . chr(13) . chr(10) .
            $citta . ' lì ' . date("d/m/Y", $data_selezionata_finale) . chr(13) . chr(10);
}
//}}} </editor-fold>

switch ($tipo_file_esportato) {
    //{{{ <editor-fold defaultstate="collapsed" desc="esporta e pubblica pdf o xls">
case "pdf":
    //cancello tutte i file temporanei fatti da più di un'ora
    /* $dir = "tmp_pdf";
      CleanFiles($dir);
      //creo i nuovi file temporanei
      $file=basename(tempnam($dir,'tmp'));
      rename($dir . "/" . $file, $dir . "/" . $file.'.pdf');
      $file.='.pdf'; */
    //Salva il PDF come file
    //$pdf->Output($dir . "/" . $file);
    $pdf->Output('stampa_tabellone_' . date('Y-m-d_H-i') . ".pdf", "D");
    exit;
        //Reindirizzamento JavaScript
        //$nuovo_nome = $dir . '/' . $file;
        //echo "<HTML><SCRIPT>document.location='$nuovo_nome';</SCRIPT></HTML>";
        break;
case "xls":
    //cancello tutte i file temporanei fatti da più di un'ora
    $dir = "tmp_xls";
    CleanFiles($dir);
    //creo i nuovi file temporanei
    $file = "stampa_tabellone_" . date('Y-m-d_H-i');
    rename($dir . "/" . $file, $dir . "/" . $file . '.xls');
    $file .= '.xls';
    //Salva il file xls come file
    $nuovo_nome = $dir . '/' . $file;
    $handle = fopen($nuovo_nome, "w");
    fwrite($handle, $testo_excel);
    fclose($handle);
    //Reindirizzamento JavaScript
    echo "<HTML><SCRIPT>document.location='$nuovo_nome';</SCRIPT></HTML>";
    break;
    //}}} </editor-fold>
}
