{literal}
    <script type="text/javascript">
        function abilitazioneSottoclassi() {
            var ids = document.getElementById("classi_secondarie").value;
            var elenco = ids.split(" ");
            var id = document.getElementById("id_classe").value;

            var pos = elenco.indexOf(id); //restituisce -1 se non lo trova, altrimenti l'indice corrispondente

            if(pos == (-1))
            { //la classe è principale
                document.getElementById("select_sottoclassi").disabled = false;
            }
            else
            { //la classe è secondaria
                document.getElementById("select_sottoclassi").disabled = true;
                document.getElementById("seleziona_sottoclassi_si").selected = true;
            }

        }
    </script>
{/literal}

{include file="header_amministratore.tpl"}

<br>
<table width='100%' class="sfondo_base_generico bordo_generico_rilievo">
    <tr>
        <td align='center' class='stampe_area_titolo'>
            {mastercom_label}Gestione Stampe: selezionare la stampa desiderata facendovi sopra un doppio click{/mastercom_label}
        </td>
        <td align='center'>
            <form method='post' action='{$SCRIPT_NAME}'>
                <input type='submit' name='bottone' value='{mastercom_label}Stampa verbali{/mastercom_label}' class='stampe_bottone'>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='stato_provenienza' value='stampe'>
                <input type='hidden' name='tipo_stampa' value='certificati'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
        </td>
    </tr>
    <tr><td><br></td></tr>
            {if $stato_secondario == "stampa_elenchi_particolari_display"}
                {* {{{ stampe varie: elenchi religione, organi collegiali *}
        <tr>
            <td width='100%' colspan="2">
                {if $tipo_stampa == "campi_liberi_wizard" and $stato_stampa != "stampa"}
                    <form method='post' action='{$SCRIPT_NAME}'>
                    {include file="stampe/wizard_stampa_campi_liberi.tpl"}
                {else}
                <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                    {if $tipo_pagella == "pagella_word_elementari_medie"}
                        <input type='hidden' name='stato_secondario' value='stampa_pagelle_template_word'>
                    {elseif $tipo_pagella == "registro_generale_voti_competenze"}
                        <input type='hidden' name='stato_secondario' value='stampa_registro_generale_voti_competenze'>
                    {elseif $tipo_pagella|strstr:"pagella_word_"}
                        <input type='hidden' name='stato_secondario' value='stampa_pagella_template_word'>
                    {else}
                        <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_update'>
                    {/if}
                {/if}
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>

                    {if $tipo_stampa == "stampa_pagelle_ministeriali"}
                        {if $tipo_pagella == "PAGELLA"}
                            {include file="stampe/pagella_ministeriale_prestampata.tpl"}
                        {elseif $tipo_pagella == "RELIGIONE"}
                            {include file="stampe/pagella_religione_prestampata.tpl"}
                        {elseif $tipo_pagella == "PAGELLA_AS2012"}
                            {include file="stampe/pagella_as2012.tpl"}
                        {elseif $tipo_pagella == "CERTIFICATO_DIPLOMA_FRONTE"}
                            {include file="stampe/certificato_diploma_fronte.tpl"}
                        {elseif $tipo_pagella == "CERTIFICATO_DIPLOMA_RETRO"}
                            {include file="stampe/certificato_diploma_retro.tpl"}
                        {elseif $tipo_pagella == "DIPLOMA"}
                            {include file="stampe/diploma.tpl"}
                        {elseif $tipo_pagella == "DIPLOMA_MEDIE"}
                            {include file="stampe/diploma_medie.tpl"}
                        {elseif $tipo_pagella == "CERTIFICATO_DIPLOMA_CARTA_LIBERA"}
                            {include file="stampe/certificato_diploma_carta_libera.tpl"}
                        {elseif $tipo_pagella == "DIPLOMA_QUALIFICA"}
                            {include file="stampe/diploma_qualifica.tpl"}
                        {elseif $tipo_pagella == "DIARIO"}
                            {include file="stampe/diario.tpl"}
                        {elseif $tipo_pagella == "CERTIFICAZIONE_COMPETENZE"}
                            {include file="stampe/certificazione_competenze.tpl"}
                        {elseif $tipo_pagella == "pagella_word_elementari_medie"}
                            {include file="stampe/pagella_word_elementari_medie.tpl"}
                        {elseif $tipo_pagella == "registro_generale_voti_competenze"}
                            {include file="stampe/registro_generale_voti_competenze.tpl"}
                        {elseif $tipo_pagella == "STAMPA_CONSIGLIO_ORIENTATIVO"}
                            {include file="stampe/stampa_consiglio_orientativo.tpl"}

                        {* {{{ stampe personalizzate *}
                        {* MARCONI (PR) *}
                        {elseif $tipo_pagella == "ss_12345_infraperiodo_marconi_pr_01"}
                            {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_marconi_pr_01.tpl"}
                        {* }}} fine stampe personalizzate*}

                        {else}
                            {include file="stampe/stampe_ministeriali.tpl"}
                        {/if}
                        <input type='hidden' name='stato_stampa_pagella' value='stampa'>
                        <input type='hidden' name='tipo_pagella' value='{$tipo_pagella}'>
                    {/if}

                    {if $tipo_pagella|strstr:"pagella_word_"}
                        {include file="stampe/pagella_word.tpl"}
                    {/if}

                    {if $tipo_stampa == "riepilogo_ore_cfp"}
                        {include file="stampe/stampa_riepilogo_ore_cfp.tpl"}
                    {/if}

                    {if $tipo_stampa == "riepilogo_esportazione_regione"}
                        {include file="stampe/riepilogo_esportazione_regione.tpl"}
                    {/if}

                    {if $tipo_stampa == "export_riepilogo_assenze"}
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' class='titolo_funzione sfondo_contrasto_generico' colspan='2'>
                                    Esportazione riepilogo assenze
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare il periodo che si intende controllare
                                </td>
                                <td class='divisore_basso'>
                                    da:
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                    a:
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Includere ritardi minimi nella stampa:
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="visualizza_rit_minimi">
                                        <OPTION selected value="NO">No</OPTION>
                                        <OPTION value="SI">Si</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat (PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per la stampa:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2' class="divisore_basso">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "vincoli_registro_presenze"}
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' colspan="2" class='titolo_funzione sfondo_contrasto_generico'>
                                    Esportazione per GRS Lombardia
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align="right" class='divisore_basso'>
                                    Selezionare la data di inizio:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align="right" class='padding_cella_generica'>
                                    Selezionare la data di fine:
                                </td>
                                <td class='padding_cella_generica'>
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2">
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "riepilogo_assenze_01"}
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' colspan="2" class='titolo_funzione sfondo_contrasto_generico'>
                                    Riepilogo delle assenze
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align="right" class='divisore_basso'>
                                    Selezionare la data di inizio:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align="right" class='padding_cella_generica'>
                                    Selezionare la data di fine:
                                </td>
                                <td class='padding_cella_generica'>
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2">
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "campi_liberi_cfp"}
                        {* {{{ sezione riepilogo assenze annuale *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align="center" class='titolo_funzione sfondo_contrasto_generico'>
                                    Riepilogo delle competenze di una classe
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la classe di cui si vuole il riepilogo:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="id_classe">
                                        {section name=cont1 loop=$classi_totali}
                                            <OPTION value="{$classi_totali[cont1][0]}">{$classi_totali[cont1][1]}{$classi_totali[cont1][2]} {$classi_totali[cont1][3]}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_marcatori_pagellina"}
                        {include file="stampe/stampa_elenco_marcatori_pagellina.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_pagellina_con_campi_liberi"}
                        {include file="stampe/stampa_pagellina_con_campi_liberi.tpl"}
                    {/if}

                    {if $tipo_stampa == "statistiche_conteggio_valutazioni_competenze_ministeriali"}
                        
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align="center" class='titolo_funzione sfondo_contrasto_generico'>
                                    Statistiche valutazioni competenze ministeriali
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='divisore_basso'>
                                    Spuntare le classi da analizzare
                                </td>
                            </tr>
                            <tr>
                                <td colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                    {/if}

                    {if $tipo_stampa == "stampa_registro_docente_materia"}
                        {include file="stampe/registro_docente_materia.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_registro_classe"}
                        {include file="stampe/registro_classe.tpl"}
                    {/if}

                    {if $tipo_stampa == "esportazione_fidae"}
                        {include file="stampe/esportazione_fidae.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_etichette"}
                        {include file="stampe/etichette_libretti.tpl"}
                    {/if}

                    {if $tipo_stampa == "elenchi_servizi_sottoscritti"}
                        {include file="stampe/elenchi_servizi_sottoscritti.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_firme_giornaliere"}
                        {include file="stampe/firme_giornaliere.tpl"}
                    {/if}

                    {if $tipo_stampa == "comparazione_voti"}
                        {include file="stampe/comparazione_voti.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_colloqui_professori"}
                        {include file="stampe/colloqui_professori.tpl"}
                    {/if}

                    {if $tipo_stampa == "mac_address_docenti"}
                        {include file="stampe/mac_address_docenti.tpl"}
                    {/if}

                    {if $tipo_stampa == "riepilogo_corsi"}
                        {include file="stampe/stampa_riepilogo_corsi.tpl"}
                    {/if}

                    {if $tipo_stampa == "riepilogo_docenti_ore_corsi"}
                        {include file="stampe/stampa_riepilogo_docenti_ore_corsi.tpl"}
                    {/if}

                    {if $tipo_stampa == "riepilogo_risorse"}
                        {include file="stampe/stampa_riepilogo_risorse.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_riepilogo_assenze_annuale"}
                        {* {{{ sezione riepilogo assenze annuale *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align="center" class='titolo_funzione sfondo_contrasto_generico'>
                                    Riepilogo Statistico Assenze di una classe
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align="center">
                                    In questa sezione è possibile definire la classe ed il periodo attraverso i quali generare e stampare un riepilogo grafico delle assenze in un unico foglio
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la classe di cui si vuole il riepilogo:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="id_classe">
                                        {section name=cont1 loop=$classi_totali}
                                            <OPTION value="{$classi_totali[cont1][0]}">{$classi_totali[cont1][3]} {$classi_totali[cont1][1]}{$classi_totali[cont1][2]}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat (PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare il periodo che si intende controllare
                                </td>
                                <td class='divisore_basso'>
                                    da:
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="MY"}
                                    a:
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="MY"}
                                    </font>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare l'intestazione che si desidera nella stampa:
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="id_sede">
                                        {section name=cont1 loop=$elenco_sedi}
                                            {if $id_sede == $elenco_sedi[cont1].id_sede}
                                                <OPTION selected value="{$elenco_sedi[cont1].id_sede}">{$elenco_sedi[cont1].descrizione_scuola} -- {$elenco_sedi[cont1].descrizione}</OPTION>
                                                {else}
                                                <OPTION value="{$elenco_sedi[cont1].id_sede}">{$elenco_sedi[cont1].descrizione_scuola} -- {$elenco_sedi[cont1].descrizione}</OPTION>
                                                {/if}
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_elenchi_barcode"}
                        {* {{{ sezione di stampa degli elenchi con i barcode *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align="center" class='titolo_funzione sfondo_contrasto_generico'>
                                    Abbinamenti codici a barre - studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align="center">
                                    In questa sezione è possibile stampare gli abbinamenti esistenti tra codici a barre e studenti
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare cosa stampare:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="id_classe">
                                        <OPTION value="tutte_istituto">Tutti gli studenti dell'istituto</OPTION>
                                        <OPTION value="tutte_classi">Tutte le classi dell'istituto</OPTION>
                                            {section name=cont1 loop=$classi}
                                            <OPTION value="{$classi[cont1][0]}">{$classi[cont1][3]} {$classi[cont1][1]}{$classi[cont1][2]}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo codice a barre da abbinare:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_codice_barcode">
                                        <OPTION selected value="badge">Tesserino</OPTION>
                                        <OPTION value="codice_giustificazioni_studente">Libretto</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Indicare il distacco desiderato (in millimetri) tra una riga e l'altra
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="distacco_desiderato">
                                        <OPTION selected value="0">0</OPTION>
                                            {for $cont_distacco=1 to 40}
                                            <OPTION value="{$cont_distacco}">{$cont_distacco}</OPTION>
                                            {/for}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "elenchi_preiscritti"}
                        {* {{{ sezione di stampa degli elenchi preiscritti *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' class='titolo_funzione sfondo_contrasto_generico'>
                                    Elenchi studenti preiscritti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align='center'>
                                    In questa sezione è possibile stampare gli elenchi degli studenti prescritti al prossimo anno scolastico
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat (PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vuole stampare la data e il luogo di nascita:
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="stampa_data_luogo_nascita">
                                        <OPTION selected value="NO">No</OPTION>
                                        <OPTION value="SI">Si</OPTION>
                                    </SELECT>
                                </td>
                            </tr>

                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare se si vogliono stampare le statistiche:
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="stampa_statistiche">
                                        <OPTION selected value="NO">No</OPTION>
                                        <OPTION value="SI">Si</OPTION>
                                    </SELECT>
                                </td>
                            </tr>

                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_elenchi"}
                        {include file="stampe/elenchi_registri.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_assenze_non_giustificate"}
                        {* {{{ sezione di stampa delle assenze non giustificate *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa riepilogo periodi assenze non giustificati
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2'>
                                    In questa sezione è possibile definire quanti periodi di assenza non giustificati deve aver fatto uno studente per essere visualizzato nella stampa.
                                    <br>E' possibile inoltre definire rispetto a che giorno si deve controllare, infatti selezionando una data verrà verificato se lo studente quel giorno era presente, se così sarà verrà controllato se esistono dei periodi di assenza non giustificati precedenti alla data selezionata.
                                    <br><br>NOTA: viene calcolato come un singolo periodo di assenza ogni serie consecutiva di giorni di assenze presenti nei dati, considerando anche la domenica.
                                    <br>Caso particolare: se uno studente è assente e il giorno dopo risulta essere assente solo al pomeriggio i periodi di assenza considerati diventano due.
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Periodi di assenza non giustificati:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="numero_assenze_minimo">
                                        <OPTION selected value="1">1</OPTION>
                                        <OPTION value="2">2</OPTION>
                                        <OPTION value="3">3</OPTION>
                                        <OPTION value="4">4</OPTION>
                                        <OPTION value="5">5</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Giorno rispetto al quale effettuare il controllo:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Esplicitare i singoli giorni d'assenza:
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="esplicita_giorni">
                                        <OPTION value="SI">SI</OPTION>
                                        <OPTION value="NO">NO</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare l'intestazione che si desidera nella stampa:
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="id_sede">
                                        {section name=cont1 loop=$elenco_sedi}
                                            {if $id_sede == $elenco_sedi[cont1].id_sede}
                                                <OPTION selected value="{$elenco_sedi[cont1].id_sede}">{$elenco_sedi[cont1].descrizione_scuola} -- {$elenco_sedi[cont1].descrizione}</OPTION>
                                                {else}
                                                <OPTION value="{$elenco_sedi[cont1].id_sede}">{$elenco_sedi[cont1].descrizione_scuola} -- {$elenco_sedi[cont1].descrizione}</OPTION>
                                                {/if}
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_assenze_classe"}
                        {* {{{ sezione stampa assenze classe *}
                        {literal}
                        <script type="text/javascript">
                        function cambio_salto_pagina(val) {
                            var tr_motivazione_assenza = $('#tr_motivazione_assenza');
                            if (val == 'studenti') {
                                tr_motivazione_assenza.show();
                            }
                            else {
                                tr_motivazione_assenza.hide();
                            }
                        }
                        </script>
                        {/literal}
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' class='titolo_funzione sfondo_contrasto_generico' colspan='2'>
                                    Stampa riepilogo assenze di una singola classe
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    In questa sezione è possibile definire la classe ed il periodo per i quali stampare un riepilogo delle assenze
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la classe di cui si vuole il riepilogo:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="id_classe">
                                        {section name=cont1 loop=$classi_totali}
                                            <OPTION value="{$classi_totali[cont1][0]}">{$classi_totali[cont1][3]} {$classi_totali[cont1][1]}{$classi_totali[cont1][2]}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vuole stampare una pagina per studente:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select name="salto_pagina" onchange="cambio_salto_pagina(this.value);"}
                                        classe###Tutta la classe insieme@@@
                                        studenti###Una pagina per studente
                                     {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr id="tr_motivazione_assenza" style='display: none;'>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vuole stampare anche la motivazione dell'assenza:
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="motivazione_assenza">
                                        <OPTION selected value="SI">Si</OPTION>
                                        <OPTION  value="NO">No</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Includere ritardi minimi nella stampa:
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="visualizza_rit_minimi">
                                        <OPTION selected value="NO">No</OPTION>
                                        <OPTION value="SI">Si</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare il periodo che si intende controllare
                                </td>
                                <td class='divisore_basso'>
                                    da:
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                    a:
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare l'intestazione che si desidera nella stampa:
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="id_sede">
                                        {section name=cont1 loop=$elenco_sedi}
                                            {if $id_sede == $elenco_sedi[cont1].id_sede}
                                                <OPTION selected value="{$elenco_sedi[cont1].id_sede}">{$elenco_sedi[cont1].descrizione_scuola} -- {$elenco_sedi[cont1].descrizione}</OPTION>
                                                {else}
                                                <OPTION value="{$elenco_sedi[cont1].id_sede}">{$elenco_sedi[cont1].descrizione_scuola} -- {$elenco_sedi[cont1].descrizione}</OPTION>
                                                {/if}
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_studenti_non_giustificati"}
                        {include file="stampe/studenti_non_giustificati.tpl"}
                    {/if}

                    {if $tipo_stampa == "curriculum"}
                        {* {{{ stampa i curriculum degli studenti delle classi selezionate *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Curriculum degli studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class="divisore_basso">
                                    Selezionare il formato di stampa:
                                </td>
                                <td width='40%' class="divisore_basso">
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION value="pdf">PDF</OPTION>
                                        <OPTION value="xls">XLS</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class="divisore_basso">
                                    Selezionare la classe di cui si vuole stampare il curriculum:
                                </td>
                                <td class="divisore_basso">
                                    <SELECT name="id_classe">
                                        <OPTION value="TUTTE">TUTTE</OPTION>
                                            {section name=cont1 loop=$classi}
                                            <OPTION value="{$classi[cont1].id_classe}">{$classi[cont1].classe} {$classi[cont1].sezione} {$classi[cont1].codice_indirizzi}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' rowspan="5" class='divisore_basso'>
                                    Selezionare se si desidera filtrare il curriculum:
                                </td>
                                <td class='padding_cella_generica'>
                                    <input type="checkbox" name="no_iscritto" value="1" checked>Eliminare le righe di iscrizione
                                </td>
                            </tr>
                            <tr>
                                <td class='padding_cella_generica'>
                                    <input type="checkbox" name="no_preiscrizione" value="1" checked>Eliminare le righe di preiscrizione
                                </td>
                            </tr>
                            <tr>
                                <td class='padding_cella_generica'>
                                    <input type="checkbox" name="no_giudizio_sospeso" value="1" checked>Eliminare le righe di giudizio sospeso
                                </td>
                            </tr>
                            <tr>
                                <td class='padding_cella_generica'>
                                    <input type="checkbox" name="no_ammissione_esame" value="1" checked>Eliminare le righe di ammissione agli esami di Stato
                                </td>
                            </tr>
                            <tr>
                                <td class='divisore_basso'>
                                    <input type="checkbox" name="no_licenziato" value="1" checked>Eliminare le righe di Licenziato
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class="divisore_basso">
                                    Selezionare se si vuole stampare la media nella riga di ammissione:
                                </td>
                                <td width='40%' class="divisore_basso">
                                    <SELECT name="media_riga_ammissione">
                                        <OPTION value="NO">NO</OPTION>
                                        <OPTION value="SI">SI</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class="divisore_basso">
                                    Selezionare se si vuole stampare i dati personali:
                                </td>
                                <td width='40%' class="divisore_basso">
                                    <SELECT name="stampa_dati_personali">
                                        <OPTION value="NO">NO</OPTION>
                                        <OPTION value="SI">SI</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class="divisore_basso">
                                    Selezionare se si vuole stampare i crediti:
                                </td>
                                <td width='40%' class="divisore_basso">
                                    <select name="stampa_crediti">
                                        <OPTION value="NO">NO</OPTION>
                                        <OPTION value="SCOLASTICI">SCOLASTICI</OPTION>
                                        <OPTION value="FORMATIVI">FORMATIVI</OPTION>
                                        <OPTION value="TUTTI">TUTTI</OPTION>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class="divisore_basso">
                                    Tipo di stampa:
                                </td>
                                <td width='40%' class="divisore_basso">
                                    <select name="stampa_tipo_pdf">
                                        <OPTION selected value="PAGINA">Pagina</OPTION>
                                        <OPTION value="ELENCO">Elenco</OPTION>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "curriculum_bianco"}
                        {* {{{ stampa i curriculum degli studenti delle classi selezionate *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Curriculum degli studenti in bianco (per compilazione manuale)
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='padding_cella_generica'>
                                    Selezionare la classe di cui si vuole stampare il curriculum:
                                </td>
                                <td width='40%' class='padding_cella_generica'>
                                    <SELECT name="id_classe">
                                        <OPTION value="TUTTE">TUTTE</OPTION>
                                            {section name=cont1 loop=$classi}
                                            <OPTION value="{$classi[cont1][0]}">{$classi[cont1][3]} {$classi[cont1][1]}{$classi[cont1][2]}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}
                    {if $tipo_stampa == "stampa_riepilogo_medie_classe"}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa del riepilogo delle medie per classe
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align='center' class='padding_cella_generica'>
                                    Selezionare le classi di cui si vuole stampare le medie:
                                </td>
                            </tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare i dati da utilizzare:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_dati_media" onchange="if (this.value === 'voti') { $('.tipo_media_sel').show(); } else { $('.tipo_media_sel').hide(); };">
                                        <OPTION selected value="voti">Voti durante l'anno</OPTION>
                                        <OPTION value="proposte">Proposte di voto pagella fine anno</OPTION>
                                        <OPTION value="pagella">Voti pagella fine anno</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            {if $media_ponderata == 'SI'}
                            <tr class="tipo_media_sel">
                                <td align='right' class='divisore_basso'>
                                    Selezionare il tipo di media:
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="tipo_media_calcolata">
                                        <OPTION selected value="ARITMETICA">Aritmetica</OPTION>
                                        <OPTION value="PONDERATA">Ponderata</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            {/if}
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la data di inizio:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {html_select_date
                                        prefix="inizio_"
                                        start_year=$anno_inizio
                                        end_year=$anno_fine
                                        day_value_format="%02d"
                                        month_format="%m"
                                        field_order="DMY"
                                        day_extra='id="inizio_giorno"'
                                        month_extra='id="inizio_mese"'
                                        year_extra='id="inizio_anno"'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la data di fine:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {html_select_date
                                        prefix="fine_"
                                        start_year=$anno_inizio
                                        end_year=$anno_fine
                                        day_value_format="%02d"
                                        month_format="%m"
                                        field_order="DMY"
                                        day_extra='id="fine_giorno"'
                                        month_extra='id="fine_mese"'
                                        year_extra='id="fine_anno"'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat(PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                    {/if}
                    {if $tipo_stampa == "foglio_notizie"}
                        {include file="stampe/foglio_notizie.tpl"}
                    {/if}

                    {if $tipo_stampa == "foglio_notizie_donbosco_bs_01"}
                        {include file="stampe/stampe_personalizzate/foglio_notizie_donbosco_bs_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "foglio_notizie_istitutofarinamestre_01"}
                        {include file="stampe/stampe_personalizzate/foglio_notizie_istitutofarinamestre_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "foglio_notizie_istitutosantadorotea_arcore_01"}
                        {include file="stampe/stampe_personalizzate/foglio_notizie_istitutosantadorotea_arcore_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "foglio_notizie_mrua_01"}
                        {include file="stampe/stampe_personalizzate/foglio_notizie_mrua_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "certificato_competenze_ee_patronato"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_ee_patronato.tpl"}
                    {/if}

                    {if $tipo_stampa == "foglio_notizie_fratellimaristicesano_mb"}
                        {include file="stampe/stampe_personalizzate/foglio_notizie_fratellimaristicesano_mb.tpl"}
                    {/if}

                    {if $tipo_stampa == "foglio_notizie_patronato_01"}
                        {include file="stampe/stampe_personalizzate/foglio_notizie_patronato_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "foglio_notizie_donboscoborgo"}
                        {include file="stampe/stampe_personalizzate/foglio_notizie_donboscoborgo.tpl"}
                    {/if}

                    {if $tipo_stampa == "organi_collegiali_studenti"}
                        {include file="stampe/organi_collegiali_studenti.tpl"}
                    {/if}

                    {if $tipo_stampa == "organi_collegiali_genitori"}
                        {include file="stampe/organi_collegiali_genitori.tpl"}
                    {/if}

                    {if $tipo_stampa == "organi_collegiali_genitori_studenti"}
                        {include file="stampe/organi_collegiali_genitori_studenti.tpl"}
                    {/if}

                    {if $tipo_stampa == "riepilogo_assenze_studente"}
                        {include file="stampe/riepilogo_assenze_studente.tpl"}
                    {/if}

                    {if $tipo_stampa == "comunicazioni_per_classe"}
                        {* {{{ stampa di tutte le comunicazioni di una/tutte le classi *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Riepilogo comunicazioni degli studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la classe di cui si vuole stampare il riepilogo:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="id_classe">
                                        <OPTION value="TUTTE">TUTTE</OPTION>
                                            {section name=cont1 loop=$classi}
                                            <OPTION value="{$classi[cont1][0]}">{$classi[cont1][3]} {$classi[cont1][1]}{$classi[cont1][2]}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare il periodo che si intende controllare:
                                </td>
                                <td class='padding_cella_generica'>
                                    da:
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                    a:
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "lista_lezioni"}
                        {* {{{ stampa di tutte le ore di lezione per materia per ogni singola classe *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa lista lezioni di una classe per materia (formato Excel)
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la classe di cui si vuole stampare la lista:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="id_classe">
                                        <OPTION value="tutte">Seleziona</OPTION>
                                            {section name=cont1 loop=$classi}
                                            <OPTION value="{$classi[cont1][0]}">{$classi[cont1][3]} {$classi[cont1][1]}{$classi[cont1][2]}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Dal:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Al:
                                </td>
                                <td class='padding_cella_generica'>
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}


                    {if $tipo_stampa == "elenco_recuperi" or $tipo_stampa == "elenco_recuperi_pai_1920"}
                        {include file="stampe/elenco_recuperi.tpl"}
                    {/if}

                    {if $tipo_stampa == "scheda_personale_candidato"}
                        {include file="stampe/scheda_personale_candidato.tpl"}
                    {/if}

                    {if $tipo_stampa == "scheda_personale_candidato_qualifiche"}
                        {include file="stampe/scheda_personale_candidato_qualifiche.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_tasse_studente"}
                        {* {{{ stampa dell'elenco delle tasse pagate dallo studente *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='5' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Selezionare le classi di cui stampare l'elenco delle tasse
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        only_main='y'
                                        bold_text='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='5'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "statistiche_promozione"}
                        {include file="stampe/statistiche_promozione.tpl"}
                    {/if}

                    {if $tipo_stampa == "elenchi_studenti"}
                        {include file="stampe/stampa_b1_new.tpl"}
                    {/if}

                    {if $tipo_stampa == "elenco_registri_classe"}
                        {* {{{ stampa degli elenchi dei numeri di registro delle classi *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa elenchi codici di registro
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la classe desiderata
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="id_classe">
                                        <OPTION selected value="TUTTE">Tutte le classi</OPTION>
                                            {section name=cont loop=$classi}
                                            <OPTION value="{$classi[cont].id_classe}">{$classi[cont].classe}{$classi[cont].sezione} {$classi[cont].codice}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat(PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                                </td>
                                <td class='padding_cella_generica'>
                                    <input type='text' name='dimensione_font' value='8' size='3'>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_riepilogo_esiti_recuperi"}
                        {include file="stampe/riepilogo_esiti_recuperi.tpl"}
                    {/if}

                    {if $tipo_stampa == "frontespizio_registro_voti"}
                        {include file="stampe/frontespizio_registro_voti.tpl"}
                    {/if}

                    {if $tipo_stampa == "statistiche_esiti_scrutini"}
                        {include file="stampe/statistiche_esiti_scrutini.tpl"}
                    {/if}

                    {if $tipo_stampa == "statistiche_tipi_recupero"}
                        {include file="stampe/statistiche_tipi_recupero.tpl"}
                    {/if}

                    {if $tipo_stampa == "statistiche_esiti_recuperi"}
                        {include file="stampe/statistiche_esiti_recuperi.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_riepilogo_assenze_monteore"}
                        {include file="stampe/riepilogo_assenze_monteore.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_monteore_corsi_classe"}
                        {include file="stampe/monteore_corsi_classe.tpl"}
                    {/if}

                    {if $tipo_stampa == "statistiche_istat" or $tipo_stampa == "statistiche_istat_non_statali" or $tipo_stampa == "statistiche_istat_scuole_medie" or $tipo_stampa == "statistiche_istat_scuole_medie_non_statali" or $tipo_stampa == "statistiche_istat_elementari" or $tipo_stampa == "statistiche_istat_elementari_non_statali" or $tipo_stampa == "statistiche_istat_infanzia" or $tipo_stampa == "statistiche_istat_infanzia_non_statali"}
                        {include file="stampe/statistiche_istat.tpl"}
                    {/if}

                    {if $tipo_stampa == "statistiche_istat_esiti_scrutini" or $tipo_stampa == "statistiche_istat_esiti_scrutini_intermedi" or $tipo_stampa == "statistiche_istat_esiti_esami" or $tipo_stampa == "statistiche_istat_esiti_scrutini_intermedi_medie" or $tipo_stampa == "statistiche_istat_esiti_scrutini_medie"}
                        {* {{{ stampa delle statistiche istat *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    {if $tipo_stampa == "statistiche_istat_esiti_scrutini"}
                                        Stampa statistiche per ministero - Esiti Scrutini Finali
                                    {elseif $tipo_stampa == "statistiche_istat_esiti_scrutini_intermedi"}
                                        Stampa statistiche per ministero - Esiti Scrutini Intermedi
                                    {elseif $tipo_stampa == "statistiche_istat_esiti_esami"}
                                        Stampa statistiche per ministero - Rilevazione integrativa degli Esami di Stato
                                    {elseif $tipo_stampa == "statistiche_istat_esiti_scrutini_intermedi_medie"}
                                        Stampa statistiche scuole medie per ministero - Esiti Scrutini Intermedi
                                    {elseif $tipo_stampa == "statistiche_istat_esiti_scrutini_medie"}
                                        Stampa statistiche scuole medie per ministero - Esiti Scrutini Finali
                                    {/if}
                                    </font>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                            <input type='hidden' name='tipo_file_esportato' value='pdf'>
                            <td align='right' width='60%' class='padding_cella_generica'>
                                Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                            </td>
                            <td width='40%' class='padding_cella_generica'>
                                <input type='text' name='dimensione_font' value='8' size='3'>
                            </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align='center' class='padding_cella_generica'>
                                    Selezionare le classi di cui si desidera stampare l'elenco
                                </td>
                            </tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "statistiche_comuni"}
                        {* {{{ stampa delle statistiche istat *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa statistica sul numero di studenti nei singoli comuni/regionI
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat(PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' rowspan="4" class='divisore_basso'>
                                    Selezionare i dati da stampare
                                </td>
                                <td class='padding_cella_generica'>
                                    <input type="checkbox" name="stampa_statistica[]" value="nascita" checked>Statistiche per comune di Nascita
                                </td>
                            </tr>
                            <tr>
                                <td class='padding_cella_generica'>
                                    <input type="checkbox" name="stampa_statistica[]" value="residenza" checked>Statistiche per comune di Residenza
                                </td>
                            </tr>
                            <tr>
                                <td class='padding_cella_generica'>
                                    <input type="checkbox" name="stampa_statistica[]" value="domicilio" checked>Statistiche per comune di Domicilio
                                </td>
                            </tr>
                            <tr>
                                <td class='divisore_basso'>
                                    <input type="checkbox" name="stampa_statistica[]" value="regione_residenza" checked>Statistiche per regione di Residenza
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di raggruppamento delle statistiche:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_ordinamento">
                                        <OPTION selected value="totale">Totali</OPTION>
                                        <OPTION value="classe">Per Classe</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per cui si vuole stampare la statistica
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='n'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "riepilogo_tasse"}
                        {include file="stampe/riepilogo_tasse.tpl"}
                    {/if}

                    {if $tipo_stampa == "nomine_rappresentanti"}
                        {include file="stampe/nomine_rappresentanti.tpl"}
                    {/if}

                    {if $tipo_stampa == "convocazioni_rappresentanti"}
                        {include file="stampe/convocazioni_rappresentanti.tpl"}
                    {/if}

                    {if $tipo_stampa == "riepilogo_rappresentanti"}
                        {include file="stampe/riepilogo_rappresentanti.tpl"}
                    {/if}

                    {if $tipo_stampa == "elenchi_rappresentanti"}
                        {include file="stampe/elenchi_rappresentanti.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_studenti_scuola_provenienza"}
                        {* {{{ stampa l'elenco degli studenti raggruppati per scuola di provenienza *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Elenco studenti suddivisi per scuola di provenienza
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td class="padding_cella_generica" align="right" width='25%'>
                                    {mastercom_label}Selezionare se si vuole stampare un elenco diviso per classi o un elenco compatto:{/mastercom_label}
                                </td>
                                <td class="padding_cella_generica" width='25%'>
                                    <SELECT name="tipo_stampa_elenco">
                                        <OPTION selected value="TABELLA_CLASSI">{mastercom_label}Elenco in tabella diviso per classi{/mastercom_label}</OPTION>
                                        <OPTION value="TABELLA">{mastercom_label}Elenco in tabella{/mastercom_label}</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per cui si vuole stampare la statistica
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='n'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                    {/if}


                    {if $tipo_stampa == "statistiche_assenze_comuni"}
                        {* {{{ stampa del riepilogo delle assenze degli studenti per comune *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa statistica sul numero di assenze degli studenti nei singoli comuni
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat(PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                                </td>
                                <td class='divisore_basso'>
                                    <input type='text' name='dimensione_font' value='8' size='3'>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare il periodo che si intende controllare
                                </td>
                                <td class='divisore_basso'>
                                    da: {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"} a: {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare il comune:
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="id_comune">
                                        <OPTION value="TUTTI">TUTTI</OPTION>
                                            {section name=cont1 loop=$array_comuni}
                                            <OPTION value="{$array_comuni[cont1].codice_comune_residenza}">{$array_comuni[cont1].descrizione}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampe_per_esami_di_stato"}
                        {include file="stampe/stampe_per_esami_di_stato.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_riepilogo_studenti_classi"}
                        {* {{{ stampa le classi selezionate con il totale degli studenti maschi e femmine *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Elenco classi selezionate con totali maschi e femmine
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare se si vuole mostrare il totale diviso per stato studente:
                                </td>
                                <td width='40%' class="divisore_basso">
                                    <SELECT name="stampa_dati">
                                        <OPTION selected value="ridotta">NO</OPTION>
                                        <OPTION value="estesa">SI</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            {foreach $elenco_optionals as $optional}
                                    {if $optional['nome'] == 'stato_studente_personalizzato' && $optional['valore'] == '1'}
                                        <tr>
                                            <td align='right' width='60%' class='divisore_basso'>
                                                Selezionare se si vuole applicare un filtro per stato personalizzato dello studente:
                                            </td>
                                            <td width='40%' class="divisore_basso">
                                                <SELECT name="filtro_stato_studente_personalizzato">
                                                    <OPTION value="TUTTI">NESSUN FILTRO</OPTION>
                                                        {section name=cont1 loop=$stati_personalizzati_studente}
                                                        <OPTION value="{$stati_personalizzati_studente[cont1].id_stato_studente_personalizzato}">{$stati_personalizzati_studente[cont1].descrizione}</OPTION>
                                                        {/section}
                                                </SELECT>
                                            </td>
                                        </tr>
                                    {/if}
                            {/foreach}

                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                   Selezionare l'ordinamento di stampa:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="ordinamento_stampa">
                                        <OPTION selected value="classe">Classe</OPTION>
                                        <OPTION value="sezione">Sezione</OPTION>
                                        <OPTION value="indirizzo">Indirizzo</OPTION>
                                        <OPTION value="classe_indirizzo">Classe, Indirizzo</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per cui si vuole stampare la statistica
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='n'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "griglia_per_classi"}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa delle griglie per classe
                                </td>
                            </tr>
                            <tr><td><br></td></tr>

                            <tr>
                                <td align='right' class='divisore_basso'>
                                    {mastercom_label}Numero giorni della settimana da prendere in considerazione:{/mastercom_label}
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select name="num_giorni_settimana"}
                                    3###3
                                    @@@4###4
                                    @@@5###5
                                    @@@6###6
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per la stampa:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat(PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                      </table>
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_videomeeting"}
                        {include file="stampe/stampa_elenco_videomeeting.tpl"}
                    {/if}

                    {if $tipo_stampa == "elenchi_compleanni"}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa dei Compleanni degli Studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica divisore_basso'>
                                    Selezionare le classi per la stampa:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2' class='divisore_basso'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' >
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat(PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                      </table>
                    {/if}

                        {if $tipo_stampa == "elenco_fratelli"}
                            <table width='100%' align='center'>
                                <tr>
                                    <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                        Elenco studenti con fratelli
                                    </td>
                                </tr>
                                <tr><td><br></td></tr>
                                <tr>
                                    <td align='center' colspan='2' class='padding_cella_generica'>
                                        Selezionare le classi per la stampa:
                                    </td>
                                </tr>
                                <tr>
                                    <td align='center' colspan='2'>
                                        {mastercom_grid_classes
                                            mat_classi=$elenco_classi_accessibili_generale
                                            mat_checks=$mat_classi
                                            onclick_submit='NO'
                                            default_background='y'
                                            onclick_set_hidden_id='id_classe'
                                            onclick_set_hidden_name='classe'
                                            status_light ='no'
                                            checks_active='si'
                                            ver2_label_bottone='label'
                                            bold_text='y'
                                            only_main='y'
                                        }
                                    </td>
                                </tr>
                                <tr><td><br></td></tr>
                                <tr>
                                    <td align='center' colspan='2'>
                                        <input type='hidden' name='tipo_file_esportato' value='xls'>
                                        <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                    </td>
                                </tr>
                            </table>
                        {/if}


                    {if $tipo_stampa == "elenco_tag"}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa dei Tag inseriti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>

                            <tr>
                                <td width='60%' align='right' class='divisore_basso'>
                                    Selezionare la data di partenza:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {html_select_date prefix="data_inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td width='60%' align='right' class='divisore_basso'>
                                    Selezionare la data di fine:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {html_select_date prefix="data_fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td width='60%' align='right' class='divisore_basso'>
                                    Selezionare se si desidera stampare tutti i tag delle classi selezionate, o quelli di un docente specifico:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tag_docente">
                                        <OPTION value="TUTTI">Tutti</OPTION>
                                        {section name=cont1 loop=$array_prof}
                                            {if $array_prof[cont1].valore != "TUTTI"}
                                            <OPTION value="{$array_prof[cont1].valore}">{$array_prof[cont1].cognome} {$array_prof[cont1].nome}</OPTION>
                                            {/if}
                                        {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td width='60%' align='right' class='divisore_basso'>
                                    Selezionare i tag:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {foreach from=$lista_tag item=tag}
                                        <input type="checkbox" name="stampa_tag[]" value={$tag['id_tag']}>{$tag['descrizione']}
                                    {/foreach}
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per la stampa:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2' class="divisore_basso">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>

                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                      </table>
                    {/if}

                    {if $tipo_stampa == "esportazioni_pago_in_rete"}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Esportazioni per PagoInRete
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per la stampa:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                      </table>
                    {/if}

                    {if $tipo_stampa == "stampa_dati_medici_studenti"}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa dei dati medici degli studenti delle classi selezionate
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per la stampa:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                      </table>
                    {/if}

                    {if $tipo_stampa == "stampa_elenchi_cartellini"}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa dei cartellini degli studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il posizionamento della foto dello studente all'interno della stampa:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="allineamento_foto">
                                        <OPTION selected value="dx">Destra</OPTION>
                                        <OPTION value="sx">Sinistra</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il formato di stampa:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="formato_stampa">
                                        <OPTION selected value="esteso">Esteso</OPTION>
                                        <OPTION value="compatto">Compatto</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di intestazione:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_intestazione_stampa">
                                        <OPTION selected value="testo">Testuale</OPTION>
                                        <OPTION value="logo">Logo</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2" class='padding_cella_generica'>
                                    Selezionare le classi per la stampa:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_classi
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                      </table>
                    {/if}

                    {if $tipo_stampa == "elenco_note_disciplinari"}
                        {include file="stampe/elenco_note_disciplinari.tpl"}
                    {/if}

                    {if $tipo_stampa == "statistiche_voti_professori"}
                        {* {{{ stampa delle statistiche relative all'inserimento dei dati dei docenti *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa statistica relativa all'inserimento di dati da parte dei docenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat(PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                                </td>
                                <td class='divisore_basso'>
                                    <input type='text' name='dimensione_font' value='10' size='3'>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Tipo di ordinamento:
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="ordinamento">
                                        <OPTION selected value="alfabetico">Alfabetico per Cognome Nome</OPTION>
                                        <OPTION value="numero_voti_totali">Per voti totali inseriti</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "statistiche_voti_studenti"}
                        {* {{{ stampa dei voti degli studenti di tutto l'istituto per periodo *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa statistica relativa ai voti assegnati a tutti gli studenti dell'istituto in un determinato periodo
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='padding_cella_generica'>
                                    Selezionare il periodo interessato:
                                </td>
                                <td width='40%' class='padding_cella_generica'>
                                    {mastercom_auto_select name="periodo_selezionato"}
                                    1###1° pagellina infraquadrimestrale@@@
                                    2###2° pagellina infraquadrimestrale@@@
                                    3###3° pagellina infraquadrimestrale@@@
                                    4###4° pagellina infraquadrimestrale@@@
                                    5###5° pagellina infraquadrimestrale@@@
                                    6###6° pagellina infraquadrimestrale@@@
                                    7###Pagella fine 1° quadrimestre/trimestre@@@
                                    8###Pagella fine 2° trimestre@@@
                                    9###Pagella fine anno
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                    <input type='hidden' name='tipo_file_esportato' value='xls'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "situazione_classe"}
                        {* {{{ sezione di stampa della situazione giornaliera di una classe *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa Situazione della classe in giorno specifico
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    In questa sezione è possibile stampare la situazione della classe per un giorno specifico.
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare la data da cui effettuare il controllo della situazione:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {html_select_date prefix="data_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare il numero di giorni da controllare (verrà stampato un foglio per ogni giorno):
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="giorni_da_stampare">
                                        {for $cont_giorni=1 to 31}
                                            <OPTION value="{$cont_giorni}">{$cont_giorni}</OPTION>
                                            {/for}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td colspan='2' valign='top' width='100%'>
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "riepilogo_permessi"}
                        {* {{{ sezione di stampa di riepilogo dei permessi assegnati agli studenti *}
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' colspan="2" class='titolo_funzione sfondo_contrasto_generico'>
                                    Riepilogo dei permessi assegnati agli studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan="2">
                                    In questa sezione è possibile stampare un riepilogo dei permessi assegnati agli studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td width="60%" align="right" class='divisore_basso'>
                                    Selezionare se si vuole stampare un foglio per classe (riepilogo compatto) o un foglio per studente:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_elenco">
                                        <OPTION selected value="compatto">Un foglio per classe</OPTION>
                                        <OPTION value="singolo">Un foglio per studente</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align="right" class='divisore_basso'>
                                    Selezionare la data da cui effettuare il controllo della situazione:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align="right" class='padding_cella_generica'>
                                    Selezionare la data fino a cui effettuare il controllo della situazione:
                                </td>
                                <td class='padding_cella_generica'>
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2">
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_permessi"}
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' colspan="2" class='titolo_funzione sfondo_contrasto_generico'>
                                    Elenco dei permessi assegnati agli studenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align="right" class='divisore_basso'>
                                    Selezionare la data da cui effettuare il controllo della situazione:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align="right" class='padding_cella_generica'>
                                    Selezionare la data fino a cui effettuare il controllo della situazione:
                                </td>
                                <td class='padding_cella_generica'>
                                    {html_select_date prefix="fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class="padding_cella_generica">
                                    {mastercom_label}Tipo di documento:{/mastercom_label}
                                </td>
                                <td class="padding_cella_generica">
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">{mastercom_label}Stampa tradizionale acrobat(PDF){/mastercom_label}</OPTION>
                                        <OPTION value="xls">{mastercom_label}Esporta in file Excel (XLS){/mastercom_label}</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2">
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                    {/if}

                    {if $tipo_stampa == "stampa_password_studenti" or $tipo_stampa == "stampa_password_genitori"}
                        {include file="stampe/credenziali_genitori_studenti.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_password_docenti"}
                        {* {{{ stampa delle password di accesso dei docenti *}
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa credenziali di accesso dei docenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align='center'>
                                    Per stampare l'elenco dei docenti con le loro password associate (solo se non sono state già cambiate) premere il pulsante di "OK" sottostante:
                                </td>
                            </tr>
                            <tr>
                                <td width='60%' align='right' class='padding_cella_generica'>
                                    Selezionare il tipo di stampa:
                                </td>
                                <td width='40%' class='padding_cella_generica'>
                                    {mastercom_auto_select name="tipo_stampa_pwd_docenti"}
                                    ELENCO###Stampa dei docenti in formato elenco (PDF)@@@
                                    LISTA_XLS###Stampa dei docenti in formato elenco (Excel)@@@
                                    SINGOLO###Stampa una pagina per docente
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Stampa credenziali accesso docenti'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_materie"}
                        {* {{{ stampa delle password di accesso dei docenti *}
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' class='titolo_funzione' colspan='2'>
                                    Per stampare l&#039;elenco delle materie premere il pulsante di "OK" sottostante:
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%'>
                                    Selezionare se si vuole un elenco generale o suddiviso per classi:
                                </td>
                                <td width='60%'>
                                    <SELECT name="tipo_elenco">
                                        <OPTION value="GENERALE">Elenco totale</OPTION>
                                        <OPTION value="CLASSI">Elenco diviso per classi</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Stampa elenco materie'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == 'stampa_reiscrizioni_interne'}
                        {include file="stampe/reiscrizioni_interne.tpl"}
                    {/if}

                    {if $tipo_stampa == 'stampa_dati_commissione'}
                        {* {{{ stampa nome commissari *}
                        <table width='100%'>
                            <tr>
                                <td align='center' colspan='4' class='titolo_funzione'>
                                    Per stampare l'elenco dei commissari diviso per commissioni premere il pulsante di "OK" sottostante:
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "statistiche_emilia_prime"}
                        {* {{{ stampa delle statistiche Emilia Romagna *}
                        <table width='100%'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa statistiche per Ufficio Scolastico Regionale Emilia Romagna - Esiti Scrutini Classi Prime
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='padding_cella_generica'>
                                    Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                                </td>
                                <td width='40%' class='padding_cella_generica'>
                                    <input type='text' name='dimensione_font' value='8' size='3'>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align='center' class='padding_cella_generica'>
                                    Selezionare le classi di cui si desidera stampare l'elenco
                                </td>
                            </tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                    <input type='hidden' name='tipo_file_esportato' value='pdf'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "statistiche_mantova"}
                        {* {{{ stampa delle statistiche per il diritto allo studio del Comune di Mantova*}
                        <table width='100%'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa delle statistiche per il diritto allo studio del Comune di Mantova
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                            <input type='hidden' name='tipo_file_esportato' value='pdf'>
                            <td align='right' width='60%' class='padding_cella_generica'>
                                Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                            </td>
                            <td width='40%' class='padding_cella_generica'>
                                <input type='text' name='dimensione_font' value='8' size='3'>
                            </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' align='center' class='padding_cella_generica'>
                                    Selezionare le classi di cui si desidera stampare l'elenco
                                </td>
                            </tr>
                            <tr>
                                <td valign='top' width='100%' colspan="2">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        bold_text='y'
                                        only_main='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_classi"}
                        {* {{{ stampa delle informazioni sulle classi *}
                        <table width='100%'>
                            <tr>
                                <td align='center' class='titolo_funzione'>
                                    Per stampare l'elenco delle classi dell'istituto premere il pulsante di "OK" sottostante:
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Stampa delle informazioni sulle classi'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_orari_classi_docenti"}
                        {include file="stampe/orari_classi_docenti.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_riepilogo_ore_corsi_per_materia"}
                        <table width='100%'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa riepiloghi orari classi
                                </td>
                            </tr>
                            <tr>
                                <td  colspan='2'>
                                    <table align='left' class="bordo_tabella_generico" width="100%">
                                        <tr class="sottotitolo_funzione">
                                            <td align='center' colspan='6' class='sfondo_contrasto_generico titolo_testo'>
                                                <b>Spuntare le classi da analizzare</b>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {mastercom_grid_classes
                                                    mat_classi=$elenco_classi_accessibili_generale
                                                    mat_checks=$mat_checks
                                                    onclick_submit='NO'
                                                    default_background='y'
                                                    onclick_set_hidden_id='id_classe'
                                                    onclick_set_hidden_name='classe'
                                                    status_light ='no'
                                                    checks_active='si'
                                                    ver2_label_bottone='label'
                                                    bold_text='y'
                                                    only_main='y'
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='center' colspan='2' class='titolo_funzione sfondo_contrasto_generico'>
                                    Selezionare i parametri finali di stampa
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare la data di partenza (compresa) del periodo che si vuole analizzare:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="data_inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare la data di fine (compresa) del periodo che si vuole analizzare:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="data_fine_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare se si vuole stampare o meno le ore impostate con campanelle rosse:
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="stampa_campanelle_rosse">
                                        <OPTION value="NO">NO</OPTION>
                                        <OPTION selected value="SI">SI</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                    {/if}

                    {if $tipo_stampa == "stampa_elenco_colloqui_prenotati"}
                        {* {{{ sezione di stampa dei colloqui prenotati *}
                        <table width='100%'>
                            <tr>
                                <td align='center' colspan="2" class='titolo_funzione sfondo_contrasto_generico'>
                                    {mastercom_label}Stampa dei colloqui prenotati{/mastercom_label}
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    {mastercom_label}Data di partenza:{/mastercom_label}
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {html_select_date prefix="data_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    {mastercom_label}Giorni da prendere in considerazione per il controllo:{/mastercom_label}
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select name="num_giorni"}
                                    01###01
                                    @@@02###02
                                    @@@03###03
                                    @@@04###04
                                    @@@05###05
                                    @@@06###06
                                    @@@07###07
                                    @@@08###08
                                    @@@09###09
                                    @@@10###10
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    {mastercom_label}Fascia oraria nella quale considerare l'inizio dei colloqui:{/mastercom_label}
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <input type="time" name="ora_inizio" value="00:00"> - <input type="time" name="ora_fine" value="23:00">
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    {mastercom_label}Testo da stampare in caso di posto non prenotato:{/mastercom_label}
                                </td>
                                <td class='divisore_basso'>
                                    <input name="testo_nessuna_prenotazione" type="textbox" value="Nessuna prenotazione per questo posto" size="50">
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    {mastercom_label}Docenti da stampare:{/mastercom_label}
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    <SELECT name="tipo_stampa_docente">
                                        {section name=cont1 loop=$array_prof}
                                            <OPTION value="{$array_prof[cont1].valore}">{$array_prof[cont1].cognome} {$array_prof[cont1].nome}</OPTION>
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare il tipo di documento:
                                </td>
                                <td width='40%' class='padding_cella_generica'>
                                    <SELECT name="tipo_file_esportato">
                                        <OPTION selected value="pdf">Stampa tradizionale acrobat (PDF)</OPTION>
                                        <OPTION value="xls">Esporta in file Excel (XLS)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_competenze_elementari"}
                        {* {{{ sezione di stampa delle competenze per scuole elementari *}
                        <table width='100%'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa delle competenze elementari
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='padding_cella_generica'>
                                    Selezionare la classe desiderata
                                </td>
                                <td width='40%' class='padding_cella_generica'>
                                    <SELECT name="id_classe">
                                        {section name=cont1 loop=$classi_totali}
                                            {if $classi_totali[cont1].tipo_indirizzo == '6'}
                                                <OPTION value="{$classi_totali[cont1][0]}">{$classi_totali[cont1][3]} {$classi_totali[cont1][1]}{$classi_totali[cont1][2]}</OPTION>
                                                {/if}
                                            {/section}
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                    {if $tipo_stampa == "stampa_cedolini_prenotazione_libri"}
                        {* {{{ sezione di stampa dei cedolini per il ritiro dei libri prenotati nella scuola dell'obbligo *}
                        <table width='100%'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione'>
                                    Stampa cedolini libri
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                </td>
                            </tr>
                        </table>
                        {* }}} *}
                    {/if}

                {* {{{ stampa pagelle personalizzate *}

                    {* Don Milani *}
                    {if $tipo_stampa == "ss_12345_finale_donmilani_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_donmilani_tn_01.tpl"}
                    {/if}

                    {* Calvino Sesto San Giovanni*}
                    {if $tipo_stampa == "mm_123_finale_calvinosestosangiovanni_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_calvinosestosangiovanni_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_calvinosestosangiovanni_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_calvinosestosangiovanni_mi_01.tpl"}
                    {/if}

                    {* Pascoli Sesto San Giovanni*}
                    {if $tipo_stampa == "ee_12345_finale_pascolisestosangiovanni_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_pascolisestosangiovanni_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_religione_pascolisestosangiovanni_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_religione_pascolisestosangiovanni_mi_01.tpl"}
                    {/if}

                    {* IC Borgo Valsugana *}
                    {if $tipo_stampa == "ee_12345_intermedia_icborgovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_icborgovalsugana_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_icborgovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_icborgovalsugana_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_icborgovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_icborgovalsugana_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_icborgovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_icborgovalsugana_tn_01.tpl"}
                    {/if}

                    {* Alberghiero Rovereto *}
                    {if $tipo_stampa == "ss_1234_generica_A3_alberghiero_rovereto_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_1234_generica_A3_alberghiero_rovereto_tn_01.tpl"}
                    {/if}

                    {* Alberghiero Levico *}
                    {if $tipo_stampa == "ss_123_generica_A3_alberghiero_levico_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_123_generica_A3_alberghiero_levico_tn_01.tpl"}
                    {/if}

                    {* IC Mezzolombardo Paganella *}
                    {if $tipo_stampa == "ee_12345_intermedia_icmezzolombardopaganella_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_icmezzolombardopaganella_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_icmezzolombardopaganella_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_icmezzolombardopaganella_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_icmezzolombardopaganella_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_icmezzolombardopaganella_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_icmezzolombardopaganella_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_icmezzolombardopaganella_tn_01.tpl"}
                    {/if}

                    {* IC Strigno Tesino *}
                    {if $tipo_stampa == "ee_12345_intermedia_icstrignotesino_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_icstrignotesino_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_icstrignotesino_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_icstrignotesino_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_icstrignotesino_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_icstrignotesino_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_icstrignotesino_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_icstrignotesino_tn_01.tpl"}
                    {/if}

                    {* IC Centro Valsugana *}
                    {if $tipo_stampa == "ee_12345_intermedia_iccentrovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_iccentrovalsugana_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_iccentrovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_iccentrovalsugana_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_iccentrovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_iccentrovalsugana_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_iccentrovalsugana_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_iccentrovalsugana_tn_01.tpl"}
                    {/if}

                    {* IC Bassa Anaunia - Tuenno *}
                    {if $tipo_stampa == "ee_12345_intermedia_icbassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_icbassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_icbassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_icbassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_icbassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_icbassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_icbassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_icbassa_tn_01.tpl"}
                    {/if}

                    {* Trento 4 *}
                    {if $tipo_stampa == "ee_12345_intermedia_trento4_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_trento4_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_trento4_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_trento4_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_trento4_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_trento4_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_trento4_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_trento4_tn_01.tpl"}
                    {/if}

                    {* Trento 6 *}
                    {if $tipo_stampa == "ee_12_generica_trento6_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12_generica_trento6_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_345_generica_trento6_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_345_generica_trento6_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_12_generica_trento6_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_12_generica_trento6_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_3_generica_trento6_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_3_generica_trento6_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_ladinadifassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_ladinadifassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_intermedia_ladinadifassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_ladinadifassa_tn_01.tpl"}
                    {/if}

                    {* Comenius *}
                    {if $tipo_stampa == "ee_12_generica_comenius_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12_generica_comenius_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_345_generica_comenius_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_345_generica_comenius_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_12_generica_comenius_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_12_generica_comenius_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_3_generica_comenius_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_3_generica_comenius_tn_01.tpl"}
                    {/if}

                    {* Fermi Cusano *}
                    {if $tipo_stampa == "ee_12345_generica_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_intermedia_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_fermi_cusano_mi_02"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_fermi_cusano_mi_02.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_religione_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_religione_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_alternativa_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_alternativa_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_alternativa_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_alternativa_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_fermi_cusano_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_fermi_cusano_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_fermi_cusano_mi_02"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_fermi_cusano_mi_02.tpl"}
                    {/if}

                    {* IC Fondo di Revo *}
                    {if $tipo_stampa == "ee_12345_intermedia_icfondo_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_icfondo_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_icfondo_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_icfondo_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_icfondo_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_icfondo_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_icfondo_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_icfondo_tn_01.tpl"}
                    {/if}

                    {* Ladina Fassa *}
                    {if $tipo_stampa == "ee_12_finale_A3_ladinadifassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12_finale_A3_ladinadifassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_345_finale_A3_ladinadifassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_345_finale_A3_ladinadifassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_12_finale_A3_ladinadifassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_12_finale_A3_ladinadifassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_3_finale_A3_ladinadifassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_3_finale_A3_ladinadifassa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_ladinadifassa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_ladinadifassa_tn_01.tpl"}
                    {/if}

                    {* IC Mezzocorona *}
                    {if $tipo_stampa == "ee_12345_intermedia_icmezzocorona_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_icmezzocorona_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_icmezzocorona_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_icmezzocorona_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_icmezzocorona_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_icmezzocorona_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_icmezzocorona_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_icmezzocorona_tn_01.tpl"}
                    {/if}

                    {* Primiero *}
                    {if $tipo_stampa == "ee_12_intermedia_primiero_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12_intermedia_primiero_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_345_intermedia_primiero_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_345_intermedia_primiero_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_primiero_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_primiero_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_primiero_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_primiero_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_primiero_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_primiero_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_primiero_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_primiero_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_A3_primiero_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_A3_primiero_tn_01.tpl"}
                    {/if}

                    {* Scholl *}
                    {if $tipo_stampa == "ss_12345_finale_A3_scholl_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_scholl_tn_01.tpl"}
                    {/if}

                    {* Rogazionisti *}
                    {if $tipo_stampa == "mm_123_infraperiodo_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_infraperiodo_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_religione_rogazionisti_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_religione_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "certificato_competenze_rogazionisti_pd_01"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_rogazionisti_pd_01.tpl"}
                    {/if}

                    {* Centromoda Canossa *}
                    {if $tipo_stampa == "ss_1234_infraperiodo_centromodacanossa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_1234_infraperiodo_centromodacanossa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_centromodacanossa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_centromodacanossa_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_1234_finale_A3_centromodacanossa_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_1234_finale_A3_centromodacanossa_tn_01.tpl"}
                    {/if}

                    {* Duomo San Giuseppe Operaio *}
                    {if $tipo_stampa == "ee_12345_finale_A3_sangiuseppe_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_sangiuseppe_pd_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "mm_123_finale_A3_sangiuseppe_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_sangiuseppe_pd_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "ee_12345_intermedia_sangiuseppe_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_sangiuseppe_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_sangiuseppe_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_sangiuseppe_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_sangiuseppe_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_sangiuseppe_pd_01.tpl"}
                    {/if}

                    {* Artigianelli *}
                    {if $tipo_stampa == "ss_12345_intermedia_artigianelli_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_artigianelli_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_artigianelli_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_artigianelli_tn_01.tpl"}
                    {/if}

                    {* Clair *}
                    {if $tipo_stampa == "ee_12345_intermedia_clair_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_clair_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_clair_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_clair_pd_01.tpl"}
                    {/if}

                    {* Gesù Maria *}
                    {if $tipo_stampa == "ee_12345_intermedia_gesumaria_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_gesumaria_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_gesumaria_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_gesumaria_pd_01.tpl"}
                    {/if}

                    {* Pergine 2 *}
                    {if $tipo_stampa == "mm_123_intermedia_pergine2_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_pergine2_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_pergine2_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_pergine2_tn_01.tpl"}
                    {/if}

                    {* Pergine 1 *}
                    {if $tipo_stampa == "mm_123_finale_pergine1_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_pergine1_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_pergine1_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_pergine1_tn_01.tpl"}
                    {/if}

                    {* Barbarigo *}
                    {if $tipo_stampa == "mm_123_intermedia_barbarigo_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_barbarigo_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_barbarigo_pd_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_barbarigo_pd_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "ss_12345_intermedia_barbarigo_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_barbarigo_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_barbarigo_pd_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_barbarigo_pd_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "mm_123_finale_A3_istitutomargherita_ba_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_istitutomargherita_ba_01.tpl"}
                    {/if}

                    {* Gardascuola *}
                    {if $tipo_stampa == "ee_12345_intermedia_gardascuola_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_gardascuola_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_gardascuola_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_gardascuola_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_gardascuola_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_gardascuola_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_gardascuola_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_gardascuola_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_gardascuola_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_gardascuola_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_gardascuola_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_gardascuola_tn_01.tpl"}
                    {/if}

                    {* mariadegliangeli bs *}
                    {if $tipo_stampa == "ee_12345_finale_A3_mariadegliangeli_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_mariadegliangeli_bs_01.tpl"}
                    {/if}

                    {* Centro Francesco lonati *}
                    {if $tipo_stampa == "ss_1234_finale_foppa_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ss_1234_finale_foppa_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "certificato_competenze_foppa_bs_01"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_foppa_bs_01.tpl"}
                    {/if}

                    {* San Benedetto *}
                    {if $tipo_stampa == "ss_12345_finale_benedetto_pc_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_benedetto_pc_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_benedetto_pc_02"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_benedetto_pc_02.tpl"}
                    {/if}

                    {*Opere Sociali Don Bosco*}
                    {if $tipo_stampa == "mm_123_infraperiodo_donbosco_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_donbosco_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_osdb_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_osdb_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_osdb_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_osdb_mi_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "ss_12345_infraperiodo_donbosco_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_donbosco_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_donbosco_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_donbosco_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_religione_osdb_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_religione_osdb_mi_01.tpl"}
                    {/if}

                    {* Arcivescovile *}
                    {if $tipo_stampa == "mm_123_infraperiodo_arcivescovile_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_arcivescovile_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_arcivescovile_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_arcivescovile_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_arcivescovile_tn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_arcivescovile_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_arcivescovile_tn_trento_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_arcivescovile_tn_trento_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_intermedia_arcivescovile_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_arcivescovile_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_arcivescovile_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_arcivescovile_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_arcivescovile_rovereto_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_arcivescovile_rovereto_tn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_arcivescovile_tn_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_arcivescovile_tn_01.tpl"}
                    {/if}

                    {* Madonna delle Nevi *}
                    {if $tipo_stampa == "ee_12345_intermedia_madonnadn_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_madonnadn_bs_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "ee_12345_finale_A3_madonnadn_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_madonnadn_bs_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "mm_123_intermedia_madonnadn_bs_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_madonnadn_bs_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "mm_123_finale_A3_madonnadn_bs_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_madonnadn_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_madonnadn_bs_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_madonnadn_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_madonnadn_bs_accoglienza_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_madonnadn_bs_accoglienza_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_infraperiodo_madonnadn_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_infraperiodo_madonnadn_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_madonnadn_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_madonnadn_bs_01.tpl"}
                    {/if}

                    {* Salesiano San Luca  *}
                    {if $tipo_stampa == "mm_123_intermedia_salesianosanluca_bo_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_salesianosanluca_bo_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_salesianosanluca_bo_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_salesianosanluca_bo_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_salesianosanluca_bo_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_salesianosanluca_bo_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_infraperiodo_salesianosanluca_bo_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_salesianosanluca_bo_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_salesianosanluca_bo_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_salesianosanluca_bo_01.tpl"}
                    {/if}

                    {* San Vincenzo de Paoli *}
                    {if $tipo_stampa == "ee_12345_generica_sanvincenzo_re_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_sanvincenzo_re_01.tpl"}
                    {/if}

                    {* Don Bosco (BS) *}
                    {if $tipo_stampa == "mm_123_infraperiodo_A4_donbosco_bs_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_A4_donbosco_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_A3_donbosco_bs_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_A3_donbosco_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_A3_donbosco_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_A3_donbosco_bs_01.tpl"}
                    {/if}

                    {* SA (MI) *}
                    {if $tipo_stampa == "ss_12345_intermedia_sa_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_sa_db_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_intermedia_A3_sa_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_A3_sa_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_religione_sa_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_religione_sa_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_sa_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_sa_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_sa_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_sa_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_sa_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_sa_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_generica_salesiano_santambrogio_mi"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_salesiano_santambrogio_mi.tpl"}
                    {/if}
                    {if $tipo_stampa == "sa_mi_db_mi_pagella_giudizi_sospesi_ss"}
                        {include file="stampe/stampe_personalizzate/sa_mi_db_mi_pagella_giudizi_sospesi_ss.tpl"}
                    {/if}

                    {* DB (MI) *}
                    {if $tipo_stampa == "ss_12345_intermedia_db_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_sa_db_mi_01.tpl"}
                    {/if}

                    {* SA (MI) e DB (MI) *}
                    {if $tipo_stampa == "ss_12345_infraperiodo_salesiano_santambrogio_mi"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_salesiano_santambrogio_mi.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_infraperiodo_salesiano_santambrogio_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_salesiano_santambrogio_mi_01.tpl"}
                    {/if}

                    {* SALESIANI CHIARI (BS) *}
                    {if $tipo_stampa == "ss_12345_generica_salesianichiari_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_salesianichiari_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_salesianichiari_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_salesianichiari_bs_01.tpl"}
                    {/if}

                    {* san francesco (AO) *}
                    {if $tipo_stampa == "ee_12345_infraperiodo_sanfrancesco_ao_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_infraperiodo_sanfrancesco_ao_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_A3_sanfrancesco_ao_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_A3_sanfrancesco_ao_01.tpl"}
                    {/if}

                    {* einaudi (AO) *}
                    {if $tipo_stampa == "ee_12345_infraperiodo_einaudi_ao_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_infraperiodo_einaudi_ao_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_A3_einaudi_ao_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_A3_einaudi_ao_01.tpl"}
                    {/if}

                    {* corjesu (MI) *}
                    {if $tipo_stampa == "ee_12345_intermedia_corjesu_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_corjesu_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_corjesu_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_corjesu_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_corjesu_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_corjesu_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_corjesu_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_corjesu_mi_01.tpl"}
                    {/if}

                    {* dameinglesi (VI) *}
                    {if $tipo_stampa == "ee_12345_intermedia_dameinglesi_vi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_dameinglesi_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_dameinglesi_vi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_dameinglesi_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_dameinglesi_vi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_dameinglesi_vi_01.tpl"}
                    {/if}

                    {* ispe (CR) *}
                    {if $tipo_stampa == "ss_12345_generica_ispe_cr_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_ispe_cr_01.tpl"}
                    {/if}

                    {* cfpnazareno *}
                    {if $tipo_stampa == "ss_12345_generica_cfpnazareno_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_cfpnazareno_01.tpl"}
                    {/if}

                    {* salesianitreviglio *}
                    {if $tipo_stampa == "mm_123_infraperiodo_salesianitreviglio_bg_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_salesianitreviglio_bg_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_salesianitreviglio_bg_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_salesianitreviglio_bg_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_intermedia_salesianitreviglio_bg_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_salesianitreviglio_bg_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_infraperiodo_salesianitreviglio_bg_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_salesianitreviglio_bg_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_salesianitreviglio_bg_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_salesianitreviglio_bg_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_salesianitreviglio_bg_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_salesianitreviglio_bg_01.tpl"}
                    {/if}

                    {if $tipo_stampa == "mm_123_intermedia_salesianivarese_va_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_salesianivarese_va_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_salesianivarese_va_02"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_salesianivarese_va_02.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_salesianivarese_va_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_salesianivarese_va_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_salesianivarese_va_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_salesianivarese_va_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_salesianitreviglio_bg_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_salesianitreviglio_bg_01.tpl"}
                    {/if}

                    {* sanbenedetto pr *}
                    {if $tipo_stampa == "mm_123_infraperiodo_sanbenedetto_pr_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_sanbenedetto_pr_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_generica_sanbenedetto_pr_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_sanbenedetto_pr_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_sanbenedetto_pr_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_sanbenedetto_pr_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_generica_sanbenedetto_pr_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_sanbenedetto_pr_01.tpl"}
                    {/if}

                    {* istitutofarina vi *}
                    {if $tipo_stampa == "ee_12345_intermedia_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_istitutofarina_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_generica_A3_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_A3_istitutofarina_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_istitutofarina_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_istitutofarina_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_istitutofarina_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_infraperiodo_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_istitutofarina_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_istitutofarina_vi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_istitutofarina_vi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_istitutofarina_vi_01.tpl"}
                    {/if}

                    {* steam bo*}
                    {if $tipo_stampa == "ss_12345_intermedia_steam_bo_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_steam_bo_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_steam_bo_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_steam_bo_01.tpl"}
                    {/if}

                    {* salesianichatillon *}
                    {if $tipo_stampa == "ss_12345_intermedia_salesianichatillon_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_salesianichatillon_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_salesianichatillon_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_salesianichatillon_01.tpl"}
                    {/if}

                    {* fratelli maristi *}
                    {if $tipo_stampa == "ee_12345_generica_fratellimaristigiugliano_na_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_fratellimaristigiugliano_na_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_infraperiodo_fratellimaristigiugliano_na_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_infraperiodo_fratellimaristigiugliano_na_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_generica_fratellimaristigiugliano_na_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_fratellimaristigiugliano_na_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_fratellimaristicesano_mb_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_fratellimaristicesano_mb_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_fratellimaristigiugliano_na_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_fratellimaristigiugliano_na_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_istitutochampagnat_ge_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_istitutochampagnat_ge_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_intermedia_istitutochampagnat_ge_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_istitutochampagnat_ge_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_istitutochampagnat_ge_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_istitutochampagnat_ge_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_istitutochampagnat_ge_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_istitutochampagnat_ge_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_sanleonemagno_rm_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_sanleonemagno_rm_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_sanleonemagno_rm_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_sanleonemagno_rm_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_sanleonemagno_rm_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_sanleonemagno_rm_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_pioxii_rm_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_pioxii_rm_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_pioxii_rm_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_pioxii_rm_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_pioxii_rm_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_pioxii_rm_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_pioxii_rm_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_pioxii_rm_01.tpl"}
                    {/if}

                    {* coverfopvercelli *}
                    {if $tipo_stampa == "ss_12345_infraperiodo_coverfopvercelli_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_coverfopvercelli_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_generica_coverfopvercelli_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_coverfopvercelli_01.tpl"}
                    {/if}

                    {* angelocustodeprimaria-bg *}
                    {if $tipo_stampa == "ee_12345_generica_angelocustodeprimaria_bg_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_angelocustodeprimaria_bg_01.tpl"}
                    {/if}

                    {* scuolasantadoroteacasalgrande-re *}
                    {if $tipo_stampa == "ee_12345_generica_A3_scuolasantadoroteacasalgrande_re_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_A3_scuolasantadoroteacasalgrande_re_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_intermedia_istitutosantadorotea_arcore_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_istitutosantadorotea_arcore_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_istitutosantadorotea_arcore_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_istitutosantadorotea_arcore_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_A3_istitutosantadorotea_arcore_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_A3_istitutosantadorotea_arcore_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_istitutosantadorotea_arcore_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_istitutosantadorotea_arcore_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_istitutosantadorotea_arcore_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_istitutosantadorotea_arcore_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "certificato_competenze_istitutosantadorotea_arcore_01"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_istitutosantadorotea_arcore_01.tpl"}
                    {/if}

                    {* bertoni-ud *}
                    {if $tipo_stampa == "ee_12345_intermedia_bertoni_ud_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_bertoni_ud_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_bertoni_ud_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_bertoni_ud_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_bertoni_ud_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_bertoni_ud_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_bertoni_ud_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_bertoni_ud_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_bertoni_ud_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_bertoni_ud_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_bertoni_ud_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_bertoni_ud_01.tpl"}
                    {/if}

                    {* csdalbenga *}
                    {if $tipo_stampa == "ss_12345_finale_A3_csdalbenga_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_csdalbenga_01.tpl"}
                    {/if}

                    {* sacrafamigliatrento *}
                    {if $tipo_stampa == "ee_12345_generica_sacrafamigliatrento_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_sacrafamigliatrento_01.tpl"}
                    {/if}

                    {* sanluigi-bo *}
                    {if $tipo_stampa == "ee_12345_generica_sanluigi_bo_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_sanluigi_bo_01.tpl"}
                    {/if}

                    {* myschoolticino *}
                    {if $tipo_stampa == "ee_12345_generica_myschoolticino_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_myschoolticino_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_finale_myschoolticino_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_myschoolticino_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "aa_finale_myschoolticino_01"}
                        {include file="stampe/pagelle_personalizzate/aa_finale_myschoolticino_01.tpl"}
                    {/if}

                    {* alexandria-al*}
                    {if $tipo_stampa == "ee_12345_generica_alexandria_al_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_alexandria_al_01.tpl"}
                    {/if}

                    {* antida-vc *}
                    {if $tipo_stampa == "ee_12345_generica_A3_antida_vc_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_A3_antida_vc_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_religione_antida_vc_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_religione_antida_vc_01.tpl"}
                    {/if}

                    {* mariadegliangeli-bs *}
                    {if $tipo_stampa == "ee_12345_intermedia_mariadegliangeli_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_mariadegliangeli_bs_01.tpl"}
                    {/if}

                    {* siaimarchetti-va *}
                    {if $tipo_stampa == "ss_12345_finale_A3_siaimarchetti_va_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_siaimarchetti_va_01.tpl"}
                    {/if}

                    {* icbonafini-bs *}
                    {if $tipo_stampa == "ee_12345_finale_icbonafini_bs_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_finale_icbonafini_bs_01.tpl"}
                    {/if}

                    {* albesteiner-to *}
                    {if $tipo_stampa == "ss_12345_finale_albesteiner_to_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_albesteiner_to_01.tpl"}
                    {/if}

                    {* liceoluzzago *}
                    {if $tipo_stampa == "ss_12345_generica_A3_liceoluzzago_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_A3_liceoluzzago_01.tpl"}
                    {/if}

                    {* donboscoborgo *}
                    {if $tipo_stampa == "ss_12345_generica_donboscoborgo_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_donboscoborgo_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_donboscoborgo_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_donboscoborgo_01.tpl"}
                    {/if}

                    {* salesianinovara *}
                    {if $tipo_stampa == "ss_12345_generica_salesianinovara_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_salesianinovara_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_salesianinovara_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_salesianinovara_01.tpl"}
                    {/if}

                    {* donboscocumiana *}
                    {if $tipo_stampa == "mm_123_generica_donboscocumiana_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_donboscocumiana_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_donboscocumiana_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_donboscocumiana_01.tpl"}
                    {/if}

                    {* leonexiii-mi *}
                    {if $tipo_stampa == "ss_12345_alternativa_leonexiii_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_alternativa_leonexiii_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_intermedia_leonexiii_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_leonexiii_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_leonexiii_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_leonexiii_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_leonexiii_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_leonexiii_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_infraperiodo_leonexiii_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_infraperiodo_leonexiii_mi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "attestato_mm_3_leone_xiii_mi"}
                        {include file="stampe/stampe_personalizzate/attestato_mm_3_leone_xiii_mi.tpl"}
                    {/if}
                    {if $tipo_stampa == "scheda_candidato_mm_3_leone_xiii_mi"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_3_leone_xiii_mi.tpl"}
                    {/if}
                    {if $tipo_stampa == "registro_voti_leone_xiii_mi"}
                        {include file="stampe/stampe_personalizzate/registro_voti_leone_xiii_mi.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_leonexiii_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_leonexiii_mi_01.tpl"}
                    {/if}

                    {* agnelli-to *}
                    {if $tipo_stampa == "mm_registro_voti_agnelli_to"}
                        {include file="stampe/stampe_personalizzate/mm_registro_voti_agnelli_to.tpl"}
                    {/if}

                    {* sacrocuoremodena *}
                    {if $tipo_stampa == "mm_123_infraperiodo_sacrocuoremodena_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_sacrocuoremodena_01.tpl"}
                    {/if}

                    {* immacolatinequinto-ge *}
                    {if $tipo_stampa == "ee_12345_intermedia_immacolatinequinto_ge_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_immacolatinequinto_ge_01.tpl"}
                    {/if}

                    {* salesianinovara *}
                    {if $tipo_stampa == "mm_123_generica_salesianinovara_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_salesianinovara_01.tpl"}
                    {/if}

                    {* br-ing *}
                    {if $tipo_stampa == "ee_12345_generica_br_ing_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_br_ing_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_br_ing_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_br_ing_01.tpl"}
                    {/if}

                    {* valsalice *}
                    {if $tipo_stampa == "ss_12345_generica_valsalice_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_valsalice_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_valsalice_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_valsalice_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_valsalice_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_valsalice_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_valsalice_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_valsalice_01.tpl"}
                    {/if}

                    {* gajo mi *}
                    {if $tipo_stampa == "ee_12345_generica_gajo_mi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_gajo_mi_01.tpl"}
                    {/if}

                    {* novalisopenschool *}
                    {if $tipo_stampa == "ss_12345_intermedia_novalisopenschool_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_intermedia_novalisopenschool_01.tpl"}
                    {/if}

                    {* valdoccoscuola *}
                    {if $tipo_stampa == "mm_123_generica_A3_valdoccoscuola_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_A3_valdoccoscuola_01.tpl"}
                    {/if}

                    {* gonzaga-pa *}
                    {if $tipo_stampa == "ee_12345_generica_gonzaga_pa_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_gonzaga_pa_01.tpl"}
                    {/if}

                    {* mrua *}
                    {if $tipo_stampa == "ee_12345_generica_mrua_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_mrua_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_mrua_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_mrua_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_mrua_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_mrua_01.tpl"}
                    {/if}

                    {* sacrocuore-no *}
                    {if $tipo_stampa == "ee_12345_intermedia_sacrocuore_no_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_intermedia_sacrocuore_no_01.tpl"}
                    {/if}

                    {* salesianibra *}
                    {if $tipo_stampa == "mm_123_generica_salesianibra_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_salesianibra_01.tpl"}
                    {/if}

                    {* csdalbenga *}
                    {if $tipo_stampa == "mm_123_generica_csdalbenga_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_csdalbenga_01.tpl"}
                    {/if}

                    {* collegiobalbi *}
                    {if $tipo_stampa == "mm_123_intermedia_collegiobalbi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_collegiobalbi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_collegiobalbi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_collegiobalbi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_collegiobalbi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_collegiobalbi_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_infraperiodo_collegiobalbi_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_infraperiodo_collegiobalbi_01.tpl"}
                    {/if}

                    {* iislagos *}
                    {if $tipo_stampa == "mm_123_generica_iislagos_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_iislagos_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_generica_iislagos_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_iislagos_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_iislagos_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_iislagos_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_religione_iislagos_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_religione_iislagos_01.tpl"}
                    {/if}

                    {* marymount-roma *}
                    {if $tipo_stampa == "ss_12345_generica_A3_marymount_roma_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_A3_marymount_roma_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_A3_marymount_roma_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_A3_marymount_roma_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_marymount_roma_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_marymount_roma_01.tpl"}
                    {/if}

                    {* viterbointernational *}
                    {if $tipo_stampa == "mm_123_generica_viterbointernationalschool_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_viterbointernationalschool_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ee_12345_generica_viterbointernationalschool_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_viterbointernationalschool_01.tpl"}
                    {/if}

                    {* masgb-bo *}
                    {if $tipo_stampa == "ee_12345_generica_A3_masgb_bo_01"}
                        {include file="stampe/pagelle_personalizzate/ee_12345_generica_A3_masgb_bo_01.tpl"}
                    {/if}

                    {* collegiogallio *}
                    {if $tipo_stampa == "ss_12345_infraperiodo_collegiogallio_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_collegiogallio_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_infraperiodo_collegiogallio_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_infraperiodo_collegiogallio_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_intermedia_collegiogallio_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_collegiogallio_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_collegiogallio_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_collegiogallio_01.tpl"}
                    {/if}


                    {* astori tv *}
                    {if $tipo_stampa == "ss_12345_infraperiodo_astori_tv_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_infraperiodo_astori_tv_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_generica_astori_tv_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_astori_tv_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_astori_tv_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_astori_tv_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_finale_A3_astori_tv_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_astori_tv_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_religione_astori_tv_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_religione_astori_tv_01.tpl"}
                    {/if}

                    {* istitutoarche cn  istitutofiore cn*}
                    {if $tipo_stampa == "mm_123_intermedia_istitutofa_cn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_istitutofa_cn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_finale_A3_istitutofa_cn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_finale_A3_istitutofa_cn_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_123_religione_A3_istitutofa_cn_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_religione_A3_istitutofa_cn_01.tpl"}
                    {/if}

                    {* istitutofarinamestre *}
                    {if $tipo_stampa == "mm_123_intermedia_istitutofarinamestre_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_intermedia_istitutofarinamestre_01.tpl"}
                    {/if}

                    {* joseftehillot mi *}
                    {if $tipo_stampa == "mm_123_generica_joseftehillot_mi_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_joseftehillot_mi_01.tpl"}
                    {/if}

                    {* salesianicaserta *}
                    {if $tipo_stampa == "mm_123_generica_A3_salesianicaserta_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_A3_salesianicaserta_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_12345_generica_A3_salesianicaserta_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_generica_A3_salesianicaserta_01.tpl"}
                    {/if}

                    {* livofamily *}
                    {if $tipo_stampa == "ss_12345_finale_A3_livofamily_01"}
                        {include file="stampe/pagelle_personalizzate/ss_12345_finale_A3_livofamily_01.tpl"}
                    {/if}

                    {* istitutofarinamestre *}
                    {if $tipo_stampa == "mm_123_generica_A3_istitutofarinamestre_01"}
                        {include file="stampe/pagelle_personalizzate/mm_123_generica_A3_istitutofarinamestre_01.tpl"}
                    {/if}


                </form>


                <form method='post' action='{$SCRIPT_NAME}' target="_blank">
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>

                {* {{{ Stampe personalizzate *}

                    {* Rogazionisti *}
                    {if $tipo_stampa == "griglia_indicatori_singolo_medie_rogazionisti_pd_01"}
                        {include file="stampe/stampe_personalizzate/griglia_indicatori_singolo_medie_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "griglia_indicatori_comparativa_medie_rogazionisti_pd_01"}
                        {include file="stampe/stampe_personalizzate/griglia_indicatori_comparativa_medie_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "griglia_indicatori_singolo_superiori_rogazionisti_pd_01"}
                        {include file="stampe/stampe_personalizzate/griglia_indicatori_singolo_superiori_rogazionisti_pd_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "griglia_indicatori_comparativa_superiori_rogazionisti_pd_01"}
                        {include file="stampe/stampe_personalizzate/griglia_indicatori_comparativa_superiori_rogazionisti_pd_01.tpl"}
                    {/if}

                    {*SA MI e DB MI*}
                    {if $tipo_stampa == "sa_mi_db_mi_modulo_debiti_aiuti_ss"}
                        {include file="stampe/stampe_personalizzate/sa_mi_db_mi_modulo_debiti_aiuti_ss.tpl"}
                    {/if}

                    {*Opere Sociali Don Bosco*}
                    {if $tipo_stampa == "osdb_registro_voti_mm"}
                        {include file="stampe/stampe_personalizzate/osdb_registro_voti_mm.tpl"}
                    {/if}

                    {if $tipo_stampa == "osdb_giudizi_comportamento_religione_mm"}
                        {include file="stampe/stampe_personalizzate/osdb_giudizi_comportamento_religione_mm.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_tabellone_osdb_mi"}
                        {include file="stampe/stampe_personalizzate/stampa_tabellone_osdb_mi.tpl"}
                    {/if}

                    {if $tipo_stampa == "osdb_verbali_recuperi_quadrimestre"}
                        {include file="stampe/stampe_personalizzate/osdb_verbali_recuperi_quadrimestre.tpl"}
                    {/if}

                    {if $tipo_stampa == "stampa_modulo_iscrizione_osdb"}
                        {include file="stampe/stampe_personalizzate/stampa_modulo_iscrizione_osdb.tpl"}
                    {/if}

                    {*Salesiani sa-mi*}
                    {if $tipo_stampa == "stampa_modulo_iscrizione_sa_mi"}
                        {include file="stampe/stampe_personalizzate/stampa_modulo_iscrizione_sa_mi.tpl"}
                    {/if}
                    {if $tipo_stampa == "sa_mi_registro_voti_mm"}
                        {include file="stampe/stampe_personalizzate/sa_mi_registro_voti_mm.tpl"}
                    {/if}
                    {if $tipo_stampa == "sa_mi_giudizi_comportamento_religione_mm"}
                        {include file="stampe/stampe_personalizzate/sa_mi_giudizi_comportamento_religione_mm.tpl"}
                    {/if}

                    {* fratellimaristicesano-mb *}
                    {if $tipo_stampa == "certificato_esame_stato_mm_fratellimaristicesano_mb"}
                        {include file="stampe/stampe_personalizzate/certificato_esame_stato_mm_fratellimaristicesano_mb.tpl"}
                    {/if}

                    {*gonzaga pa*}
                    {if $tipo_stampa == "gonzaga_pa_giudizio_tutoriale_ss"}
                        {include file="stampe/stampe_personalizzate/gonzaga_pa_giudizio_tutoriale_ss.tpl"}
                    {/if}

                    {*madonnadn bs*}
                    {if $tipo_stampa == "tabellone_mm_madonnadn_bs"}
                        {include file="stampe/stampe_personalizzate/tabellone_mm_madonnadn_bs.tpl"}
                    {/if}
                    {if $tipo_stampa == "registro_voti_ss_madonnadn_bs"}
                        {include file="stampe/stampe_personalizzate/registro_voti_ss_madonnadn_bs.tpl"}
                    {/if}

                    {*salesiani treviglio bg*}
                    {if $tipo_stampa == "tabellone_ee_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/tabellone_ee_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "tabellone_mm_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/tabellone_mm_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "tabellone_ss_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/tabellone_ss_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_modulo_mdi_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/ss_modulo_mdi_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "certificato_competenze_medie_12_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_medie_12_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "registri_segreteria_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/registri_segreteria_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "consiglio_orientativo_mm_3_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/consiglio_orientativo_mm_3_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_3_assegnazione_tutor_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/mm_3_assegnazione_tutor_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "scheda_candidato_mm_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_salesianitreviglio_bg.tpl"}
                    {/if}
                    {if $tipo_stampa == "certificato_competenze_ss_4_salesianitreviglio_bg"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_ss_4_salesianitreviglio_bg.tpl"}
                    {/if}

                    {*donbosco bs*}
                    {if $tipo_stampa == "certificato_competenze_donbosco_bs_01"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_donbosco_bs_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "scheda_softskills_ss1_donbosco_bs_01"}
                        {include file="stampe/stampe_personalizzate/scheda_softskills_ss1_donbosco_bs_01.tpl"}
                    {/if}

                    {* bertoni ud *}
                    {if $tipo_stampa == "ss_profilo_personale_bertoni_ud"}
                        {include file="stampe/stampe_personalizzate/ss_profilo_personale_bertoni_ud.tpl"}
                    {/if}

                    {* salesianivarese va *}
                    {if $tipo_stampa == "salesianivarese_va_registro_voti_mm"}
                        {include file="stampe/stampe_personalizzate/salesianivarese_va_registro_voti_mm.tpl"}
                    {/if}

                    {* donboscoborgo *}
                    {if $tipo_stampa == "educativi_mm_123_generica_donboscoborgo_01"}
                        {include file="stampe/stampe_personalizzate/educativi_mm_123_generica_donboscoborgo_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "educativi_ss_12345_generica_donboscoborgo_01"}
                        {include file="stampe/stampe_personalizzate/educativi_ss_12345_generica_donboscoborgo_01.tpl"}
                    {/if}

                    {* mrua *}
                    {if $tipo_stampa == "certificato_competenze_mrua"}
                        {include file="stampe/stampe_personalizzate/certificato_competenze_mrua.tpl"}
                    {/if}

                    {* smt-mi *}
                    {if $tipo_stampa == "statistiche_uomini_donne_smt_mi"}
                        {include file="stampe/stampe_personalizzate/statistiche_uomini_donne_smt_mi.tpl"}
                    {/if}

                    {* valdoccoscuola *}
                    {if $tipo_stampa == "scheda_candidato_mm_3_valdoccoscuola"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_3_valdoccoscuola.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_registro_voti_valdoccoscuola_01"}
                        {include file="stampe/stampe_personalizzate/mm_registro_voti_valdoccoscuola_01.tpl"}
                    {/if}

                    {* valsalice *}
                    {if $tipo_stampa == "mm_registro_voti_valsalice_01"}
                        {include file="stampe/stampe_personalizzate/mm_registro_voti_valsalice_01.tpl"}
                    {/if}

                    {* donboscoborgo *}
                    {if $tipo_stampa == "scheda_candidato_mm_donboscoborgo"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_donboscoborgo.tpl"}
                    {/if}

                    {* marymount-roma *}
                    {if $tipo_stampa == "scheda_candidato_mm_marymount_roma"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_marymount_roma.tpl"}
                    {/if}

                    {* salesianinovara *}
                    {if $tipo_stampa == "scheda_candidato_mm_salesianinovara"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_salesianinovara.tpl"}
                    {/if}

                    {* csdalbenga *}
                    {if $tipo_stampa == "registro_voti_mm_csdalbenga"}
                        {include file="stampe/stampe_personalizzate/registro_voti_mm_csdalbenga.tpl"}
                    {/if}
                    {if $tipo_stampa == "registro_voti_ss_csdalbenga"}
                        {include file="stampe/stampe_personalizzate/registro_voti_ss_csdalbenga.tpl"}
                    {/if}

                    {* iislagos *}
                    {if $tipo_stampa == "ss_registro_voti_iislagos_01"}
                        {include file="stampe/stampe_personalizzate/ss_registro_voti_iislagos_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "ss_registro_voti_generale_iislagos_01"}
                        {include file="stampe/stampe_personalizzate/ss_registro_voti_generale_iislagos_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_registro_voti_iislagos_01"}
                        {include file="stampe/stampe_personalizzate/mm_registro_voti_iislagos_01.tpl"}
                    {/if}
                    {if $tipo_stampa == "mm_registro_voti_generale_iislagos_01"}
                        {include file="stampe/stampe_personalizzate/mm_registro_voti_generale_iislagos_01.tpl"}
                    {/if}

                    {* liceoluzzago *}
                    {if $tipo_stampa == "registro_voti_ss_liceoluzzago_01"}
                        {include file="stampe/stampe_personalizzate/registro_voti_ss_liceoluzzago_01.tpl"}
                    {/if}

                    {* astori-tv *}
                    {if $tipo_stampa == "astori_tv_ss_12345_lettera_01"}
                        {include file="stampe/stampe_personalizzate/astori_tv_ss_12345_lettera_01.tpl"}
                    {/if}

                    {* collegiogallio *}
                    {if $tipo_stampa == "scheda_candidato_mm_collegiogallio"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_collegiogallio.tpl"}
                    {/if}

                    {* collegiobalbi *}
                    {if $tipo_stampa == "collegiobalbi_tabellone_esiti_ee"}
                        {include file="stampe/stampe_personalizzate/collegiobalbi_tabellone_esiti_ee.tpl"}
                    {/if}
                    {if $tipo_stampa == "collegiobalbi_tabellone_esiti_mm"}
                        {include file="stampe/stampe_personalizzate/collegiobalbi_tabellone_esiti_mm.tpl"}
                    {/if}
                    {if $tipo_stampa == "scheda_candidato_mm_3_collegiobalbi"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_3_collegiobalbi.tpl"}
                    {/if}

                    {if $tipo_stampa == "scheda_candidato_mm_arcivescovile_tn"}
                        {include file="stampe/stampe_personalizzate/scheda_candidato_mm_arcivescovile_tn.tpl"}
                    {/if}

                    {* altro *}
                    {if $tipo_stampa == "contabilizzazione_doposcuola"}
                        {include file="stampe/stampe_personalizzate/contabilizzazione_doposcuola.tpl"}
                    {/if}
                </form>

                {if $tipo_stampa == "elenco_assenti_giornalieri"}
                    {* {{{ stampa degli studenti assenti in un determinato giorno *}
                    <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                        <table width='100%' align='center'>
                            <tr>
                                <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                    Stampa riepilogo assenti
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' width='60%' class='divisore_basso'>
                                    Selezionare se si vuole un foglio per classe o una stampa compatta:
                                </td>
                                <td width='40%' class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_un_foglio_per_classe name='un_foglio_per_classe'}
                                    SI###Un foglio per classe@@@
                                    NO###Stampa compatta
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vuole stampare uno studente per riga o fino a tre studenti per riga:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_studente_singolo name='studente_singolo'}
                                    SINGOLO###Uno studente per riga(necessario per la stampa del codice a barre)@@@
                                    MULTI###Tre studenti per riga
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vuole stampare il codice a barre del tesserino dello studente:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_codice_barre name='codice_barre'}
                                    SI###SI@@@
                                    NO###NO
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vuole stampare la firma del docente:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_firma_docente name='firma_docente'}
                                    SI###SI@@@
                                    NO###NO
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vuole stampare il riquadro delle note al termine di ogni classe:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_note name='note'}
                                    SI###SI@@@
                                    NO###NO
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vogliono stampare anche le classi che non presentano assenti:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_stampa_tutte_le_classi name='stampa_tutte_le_classi'}
                                    SI###SI@@@
                                    NO###NO
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare se si vogliono stampare le classi selezionate o i gruppi di classi esistenti:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_stampa_gruppi name='stampa_gruppi'}
                                    SI###I gruppi creati@@@
                                    NO###Le singole classi selezionate
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare la dimensione del font da utilizzare:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_dimensione_font name='dimensione_font'}
                                    8###8@@@
                                    9###9@@@
                                    10###10@@@
                                    11###11@@@
                                    12###12@@@
                                    13###13@@@
                                    14###14@@@
                                    15###15@@@
                                    16###16@@@
                                    17###17@@@
                                    18###18@@@
                                    19###19@@@
                                    20###20
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare quali studenti si desidera stampare:
                                </td>
                                <td class='divisore_basso'>
                                    {mastercom_auto_select value=$rapportino_stampa_assenti name='stampa_assenti'}
                                    SOLO_ASSENTI###Stampa solo gli studenti assenti al momento della stampa@@@
                                    ASSENTI_E_RITARDI###Stampa gli studenti assenti e entrati in ritardo al momento della stampa@@@
                                    PRESENTI###Stampa solo gli studenti presenti al momento della stampa@@@
                                    TUTTI###Stampa tutti gli studenti
                                    {/mastercom_auto_select}
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Giorno rispetto al quale effettuare il controllo:
                                </td>
                                <td class='divisore_basso'>
                                    {html_select_date prefix="inizio_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='5' class='titolo_funzione sfondo_contrasto_generico'>
                                    Spuntare le classi di cui si desidera stampare il riepilogo oppure spuntare la casella 'Tutte le classi' per stampare l'elenco completo
                                </td>
                            </tr>
                            <tr>
                                <td align='right' colspan='2'>
                                    <table width="100%" class="bordo_tabella_generico">
                                        <tr>
                                            <td colspan='5' align='center' class='divisore_basso'>
                                                {mastercom_smart_checkbox nome_pulsante="visualizzazione_abbinamenti" nome_checkbox="checkbox_tutte" contesto="istituto" checked="NO" valore="1" utente_corrente=$current_user}
                                                Tutte le classi
                                            </td>
                                        </tr>
                                        {section name=cont_ext loop=$start}
                                            <tr>
                                                {section name=cont loop=$dati_checkbox start=$start[cont_ext] max=5 step=$step}
                                                    <td>
                                                        {mastercom_smart_checkbox nome_pulsante="visualizzazione_abbinamenti" nome_checkbox="checkbox`$dati_checkbox[cont][0]`" contesto="istituto" checked="NO" valore="1" utente_corrente=$current_user}
                                                        {$dati_checkbox[cont][1]}{$dati_checkbox[cont][2]} {$dati_checkbox[cont][3]}<br>
                                                    </td>
                                                {/section}
                                            </tr>
                                        {/section}
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan='2'>
                                    {if $gate_in_corso == 'SI'}
                                        <font color='#bb2222'>Attenzione! Processo automatico di inserimento assenze in corso. Attendere il termine prima di effettuare la stampa.</font>
                                    {else}
                                        <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                        <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_update'>
                                        <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    {/if}
                                </td>
                            </tr>
                        </table>
                    </form>
                    <br><br>
                    <table width='100%' align='center'>
                        <tr>
                            <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                Definizione gruppi di stampa
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                                {if $messaggio != ''}
                            <tr>
                                <td colspan='2' align='center' style='font-weight: bold; color:red;'>
                                    {$messaggio}
                                </td>
                            </tr>
                        {/if}
                        {if count($elenco_gruppi_stampa) > 0}
                            <tr>
                                <td colspan='2' align='center'>
                                    <table width='100%' align='center'>
                                        <tr>
                                            <td align='center' colspan='5' class='titolo_funzione'>
                                                Gruppi esistenti
                                            </td>
                                        </tr>
                                        {foreach from=$elenco_gruppi_stampa item=singolo_gruppo}
                                            <tr>
                                                <td class='sfondo_contrasto_generico'>
                                                    {$singolo_gruppo.classi_stampabili}
                                                </td>
                                                <td align='center' class='sfondo_contrasto_generico'>
                                                    <form method='post' action='{$SCRIPT_NAME}'>
                                                        <input type='image' name='bottone' value='Elimina' src='icone/icona.php?icon=delete&size=38' alt='Elimina'>
                                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                        <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                                                        <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                                                        <input type='hidden' name='stato_gruppo' value='elimina_gruppo'>
                                                        <input type='hidden' name='id_gruppo_stampa' value='{$singolo_gruppo.id_gruppo_stampa}'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </form>
                                                </td>
                                            </tr>
                                        {/foreach}
                                    </table>
                                </td>
                            </tr>
                        {else}
                            <tr>
                                <td colspan='2' align='center'>
                                    Nessun gruppo definito
                                </td>
                            </tr>
                        {/if}
                        <tr><td><br></td></tr>
                        <tr>
                            <td colspan='5' class='sfondo_contrasto_generico' align='center'>
                                Selezionare le classi di cui si vuole creare un gruppo di stampa
                            </td>
                        </tr>
                        <tr>
                            <td align='right' colspan='2'>
                                <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                    <table width="100%" class="bordo_tabella_generico">
                                        {section name=cont_ext loop=$start}
                                            <tr>
                                                {section name=cont loop=$dati_checkbox start=$start[cont_ext] max=5 step=$step}
                                                    <td>
                                                        {mastercom_smart_checkbox nome_pulsante="visualizzazione_abbinamenti" nome_checkbox="checkbox`$dati_checkbox[cont][0]`" contesto="istituto" checked="NO" valore="1" utente_corrente=$current_user}
                                                        {$dati_checkbox[cont][1]}{$dati_checkbox[cont][2]} {$dati_checkbox[cont][3]}<br>
                                                    </td>
                                                {/section}
                                            </tr>
                                        {/section}
                                        <tr>
                                            <td align='center' colspan='5'>
                                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                                                <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                                                <input type='hidden' name='stato_gruppo' value='crea_gruppo'>
                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </td>
                        </tr>
                    </table>
                    {* }}} *}
                {/if}

                {if $tipo_stampa == "certificati"}
                    {include file="stampe/certificati.tpl"}
                {/if}

                {if $tipo_stampa == "stampa_abbinamenti"}
                    {* {{{ stampa abbinamenti per consigli di classe *}
                    <table width='100%'>
                        <tr>
                            <td colspan='5' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                Stampa abbinamenti
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr>
                            <td colspan='4' align='right' width='60%' class='divisore_basso'>
                                Selezionare ciò di cui si desidera stampare gli abbinamenti
                            </td>
                            <td width='40%' class='divisore_basso'>
                                <select onchange="
                                        document.getElementById('tipo_file_esportato_1').value = this.value;
                                        document.getElementById('tipo_file_esportato_2').value = this.value;
                                        document.getElementById('tipo_file_esportato_3').value = this.value;
                                        ">
                                    <option value='pdf'>PDF</option>
                                    <option value='xls'>XLS</option>
                                </select>
                            </td>
                        </tr>
                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                            <tr>
                                <td align='right' class='divisore_basso'>
                                    Selezionare il professore
                                </td>
                                <td colspan='3' align='right' width='10%' class='divisore_basso'>

                                    {mastercom_smart_select nome_pulsante="visualizzazione_abbinamenti" contesto="istituto" nome_select="id_professore" utente_corrente=$current_user}
                                    <OPTION value="TUTTI_PROF">Tutti i professori</OPTION>
                                    {section name=cont1 loop=$array_prof}
                                    <OPTION value="{$array_prof[cont1].valore}">{$array_prof[cont1].cognome} {$array_prof[cont1].nome}</OPTION>
                                    {/section}
                                    {/mastercom_smart_select}
                                </td>
                                <td class='divisore_basso'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                    <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_update'>
                                    <input type='hidden' name='tipo_stampa' value='stampa_abbinamenti'>
                                    <input type='hidden' id='tipo_file_esportato_1' name='tipo_file_esportato' value=''>
                                    <input type='hidden' name='form_stampa' value='professore'>
                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                </td>
                            </tr>
                        </form>
                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                            <tr>
                                <td align='right' width="25%" class='divisore_basso'>
                                    Selezionare la materia
                                </td>
                                <td colspan='3' align='right' width='25%' class='divisore_basso'>
                                    {mastercom_smart_select nome_pulsante="visualizzazione_abbinamenti" contesto="istituto" nome_select="id_materia" utente_corrente=$current_user}
                            <OPTION value="TUTTE_MATERIE">Tutte le materia</OPTION>
                                {section name=cont1 loop=$materie}
                                    {if $materie[cont1][7] != "materia_itp"}
                                    <OPTION value="{$materie[cont1][0]}">{$materie[cont1][2]}</OPTION>
                                    {/if}
                                {/section}
                                {/mastercom_smart_select}
                            </td>
                            <td class='divisore_basso'>
                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_update'>
                                <input type='hidden' name='tipo_stampa' value='stampa_abbinamenti'>
                                <input type='hidden' id='tipo_file_esportato_2' name='tipo_file_esportato' value=''>
                                <input type='hidden' name='form_stampa' value='materia'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </td>
                            </tr>
                        </form>
                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Selezionare la classe
                                </td>
                                <td align='right' width='25%' class='padding_cella_generica'>
                                    <select id="id_classe" name="id_classe" onchange="abilitazioneSottoclassi();">
{*                                    {mastercom_smart_select nome_pulsante="visualizzazione_abbinamenti" contesto="istituto" nome_select="id_classe" utente_corrente=$current_user onchange="disabilita();"}*}
                                    <OPTION value="TUTTE_CLASSI">Tutte le classi</OPTION>
                                        {section name=cont1 loop=$classi}
                                        <OPTION value="{$classi[cont1][0]}">{$classi[cont1][1]}{$classi[cont1][2]} {$classi[cont1][3]}</OPTION>
                                        {/section}
{*                                        {/mastercom_smart_select}*}
                                    </select>
                                </td>
                                <td align='right' class='padding_cella_generica'>
                                    Includere le sottoclassi
                                </td>
                                <td align='right' class='padding_cella_generica'>
                                    <select id='select_sottoclassi' onchange="
                                        document.getElementById('includi_sottoclassi').value = this.value;
                                        ">
                                        <option value='no'>NO</option>
                                        <option id="seleziona_sottoclassi_si" value='si'>SI</option>
                                    </select>
                                </td>
                            <td class='padding_cella_generica'>
                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_update'>
                                <input type='hidden' name='tipo_stampa' value='stampa_abbinamenti'>
                                <input type='hidden' id='tipo_file_esportato_3' name='tipo_file_esportato' value=''>
                                <input type='hidden' name='form_stampa' value='classe'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                                <input type='hidden' id='includi_sottoclassi' name='includi_sottoclassi' value='no'>
                                <input type='hidden' id='classi_secondarie' name='classi_secondarie' value="{section name = cont2 loop=$classi_secondarie}{$classi_secondarie[cont2][0]} {/section}">
                            </td>
                            </tr>
                            <tr>
                                <td colspan='5' align="center">
                                    (attenzione, con l'opzione multiclasse attiva, selezionando una singola classe verranno stampati anche gli eventuali abbinamenti delle classi collegate agli studenti della classe selezionata)
                                </td>
                            </tr>
                        </form>
                    </table>
                    {* }}} *}
                {/if}

                {if $tipo_stampa == "stampa_moduli_master"}
                    {* {{{ sezione di visualizzazione dell'elenco dei moduli master disponibili *}
                    <table width='100%' align='center'>
                        <tr>
                            <td align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                In questa sezione è possibile stampare il modello selezionato
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr>
                            <td align='center'>
                                <a style='font-size:16px' href="modelli_generali/configurazione_totem.pdf" target='_blank'>Modello per configurare il comportamento dei sistemi di rilevazione presenze</a>
                            </td>
                        </tr>
                        <tr>
                            <td align='center'>
                                <a style='font-size:16px' href="modelli_generali/configurazione_mensa.pdf" target='_blank'>Modello per configurare le impostazioni del servizio mensa</a>
                            </td>
                        </tr>
                    </table>
                    {* }}} *}
                {/if}
            </td>
        </tr>
        {* }}} *}
    {/if}
    <tr><td><br></td></tr>
    <tr>
        <td width='100%' colspan='2' class='padding_cella_generica'>
            {include file="stampe/selettore_stampe.tpl"}
        </td>
    </tr>
</table>

{include file="footer_amministratore.tpl"}
