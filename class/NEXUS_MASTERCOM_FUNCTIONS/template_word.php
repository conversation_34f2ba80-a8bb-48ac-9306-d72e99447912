<?php

// $tipo_template -> 'singolo' o 'gruppo'
// $tipo_oggetti -> 'studenti' (per il momento)
function  estrai_dati_template_word($variabili_template, $tipo_template, $id_oggetti, $tipo_oggetti_input){

    $stringa_anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $inizio_anno_scolastico = estrai_parametri_singoli("DATA_INIZIO_ANNO_SCOLASTICO");
    $fine_anno_scolastico = estrai_parametri_singoli("DATA_FINE_ANNO_SCOLASTICO");
    $anno_scolastico_settato = explode("/", $stringa_anno_scolastico);
    $anno_inizio = intval($anno_scolastico_settato[0]);
    $anno_fine = intval($anno_scolastico_settato[1]);
    $array_inizio_anno_scolastico = explode("/", $inizio_anno_scolastico);
    $array_fine_anno_scolastico = explode("/", $fine_anno_scolastico);
    $giorno_inizio_anno_scolastico = intval($array_inizio_anno_scolastico[0]);
    $giorno_fine_anno_scolastico = intval($array_fine_anno_scolastico[0]);
    $mese_inizio_anno_scolastico = intval($array_inizio_anno_scolastico[1]);
    $mese_fine_anno_scolastico = intval($array_fine_anno_scolastico[1]);
    $data_inizio_check = "$anno_inizio-$mese_inizio_anno_scolastico-$giorno_inizio_anno_scolastico";
    $data_fine_check = "$anno_fine-$mese_fine_anno_scolastico-$giorno_fine_anno_scolastico";

    $tipo_oggetti_input = strtoupper($tipo_oggetti_input);
    $return = [];

    // intabella database anche colonna con jsona per specifiche campi tabella
    $sql = "SELECT id_corrispondenza_campo_word,
                campo,
                tipologia,
                dati_aggiuntivi
            FROM corrispondenze_campi_word
            WHERE flag_canc = 0
    ";
    $result = pgsql_query($sql) or die("Invalid $sql");
    $corrispondenze_campi_tabelle_tmp = pg_fetch_all($result);

    $corrispondenze_campi_tabelle = [];
    foreach ($corrispondenze_campi_tabelle_tmp as $campo){
        try {
            $campo['dati_aggiuntivi'] = json_decode($campo['dati_aggiuntivi'], true);
        } catch (Exception $e){ };

        $corrispondenze_campi_tabelle[$campo['campo']] = $campo;
    }

    $elenco_campi = [];

    foreach ($corrispondenze_campi_tabelle as $campo) {
        foreach ($variabili_template as $variabile) {
            if ($variabile == $campo['campo']) {
                $elenco_campi[$campo['tipologia']][$campo['campo']]['nome'] = $campo['campo'];
            }
        }
    }

    foreach ($elenco_campi as $tipologia => $campo){
        switch ($tipologia) {
            case 'studenti':
                // estrazione dati studenti
                $where = "";
                switch ($tipo_oggetti_input) {
                    case 'STUDENTI':
                        $where = " id_studente IN (".implode(', ', $id_oggetti).") ";
                        break;
                    case 'PARENTI':
                        $where = " id_studente IN (
                            SELECT id_studente
                            FROM parenti_studenti
                            WHERE flag_canc = 0
                                AND id_parente IN (".implode(', ', $id_oggetti).")
                        ) ";
                        break;
                    default:
                        break;
                }
                $query = "SELECT *
                    FROM studenti_completi
                    WHERE {$where}
                    ORDER BY
                        cognome,
                        nome,
                        ordinamento DESC";

                $result = pgsql_query($query) or die("Invalid $query");
                $numero = pg_num_rows($result);
                $studenti_tmp = pg_fetch_all($result);

                $elenco_studenti = [];
                foreach ($studenti_tmp as $studente){
                    // dati generali
                    foreach ($studente as $key =>  $value){
                        $studente[$key] = decode($value);
                    }

                    // dati personalizzati
                    $dati_personalizzati = estrai_dati_personalizzati_studente($studente['id_studente']);
                    foreach ($dati_personalizzati as $id_dato_pers =>  $dato_pers){
                        foreach ($dato_pers as $k => $v) {
                            $dato_pers[$k] = decode($v);
                        }
                        $studente[$id_dato_pers] = $dato_pers;
                    }

                    // movimenti pagamenti
                    $array_movimenti = estrai_movimenti_studente((int) $studente['id_studente'], $data_inizio_check, $data_fine_check, 'TUTTI', 'data_scadenza');

                    if(is_array($array_movimenti)){
                        foreach ($array_movimenti as $k => $v) {
                            $array_movimenti[$k] = decode($v);
                        }
                        $studente['movimenti_studente'] = $array_movimenti;
                    }

                    // dati hotspot
                    $query_hot_spot = "SELECT * FROM client_hot_spot
                                        WHERE id_utente = {$studente['id_studente']}
                                        AND tipo_utente = 'S' AND flag_canc = 0";
                    $result_hot_spot = pgsql_query($query_hot_spot);
                    $numero_hot_spot = pg_num_rows($result_hot_spot);
                    if ($numero_hot_spot > 0) {
                        $row_hot_spot = pg_fetch_assoc($result_hot_spot, 0);
                        $studente['abilitazione_navigazione'] = $row_hot_spot['attivo'];
                        $studente['descrizione_dispositivo'] = decode($row_hot_spot['descrizione']);
                        $studente['mac_address'] = decode($row_hot_spot['mac_address']);
                    }

                    // curriculum
                    $dati_curriculum = estrai_curriculum_studente((int) $studente['id_studente'], $no_iscrizione = false, $no_giudizio_sospeso = false, $no_ammissione = false, $no_preiscrizione = false, $no_licenziato = false, $no_ritirato = false, $id_classe_controllo = '', $no_trasferito = false);
                    if(is_array($dati_curriculum)){
                        $studente['dati_curriculum'] = $dati_curriculum;
                    }

                    // dati commissione esame terza media
                    $mat_commissioni = estrai_classi_sottocommissioni_commissione_medie($studente['id_classe']);
                    if(is_array($mat_commissioni)){
                        $studente['esame_medie_commissione'] = $mat_commissioni;
                    }

                    if (!isset($elenco_studenti[$studente['id_studente']])){
                        $elenco_studenti[$studente['id_studente']] = $studente;
                    }
                    $elenco_studenti[$studente['id_studente']]['classi'][$studente['id_classe']]['id_classe'] = $studente['id_classe'];
                    $elenco_studenti[$studente['id_studente']]['classi'][$studente['id_classe']]['classe'] = $studente['classe'];
                    $elenco_studenti[$studente['id_studente']]['classi'][$studente['id_classe']]['sezione'] = $studente['sezione'];
                    $elenco_studenti[$studente['id_studente']]['classi'][$studente['id_classe']]['descrizione_indirizzi'] = $studente['descrizione_indirizzi'];
                    $elenco_studenti[$studente['id_studente']]['classi'][$studente['id_classe']]['ordinamento'] = $studente['ordinamento'];

                }
                break;
            /* ------------------------------------------------------------------------------------------------------------------------- */
            case 'parenti':
                // estrazione dati parenti
                $where_parenti = "";
                $where_parenti_studenti = "";
                switch ($tipo_oggetti_input) {
                    case 'STUDENTI':
                        $where_parenti = " id_parente IN (
                            SELECT id_parente
                            FROM parenti_studenti
                            WHERE flag_canc = 0
                                AND id_studente IN (" . implode(', ', $id_oggetti) . ")
                        ) ";
                        $where_parenti_studenti = " id_studente IN (" . implode(', ', $id_oggetti) . ") ";
                        break;
                    case 'PARENTI':
                        $where_parenti = $where_parenti_studenti = " id_parente IN (" . implode(', ', $id_oggetti) . ") ";
                        break;
                    default:
                        break;
                }

                //parenti
                $query = "SELECT *
                    FROM parenti
                    WHERE {$where_parenti}
                    ORDER BY
                        cognome,
                        nome";

                $result = pgsql_query($query) or die("Invalid $query");
                $numero = pg_num_rows($result);
                $parenti_tmp = pg_fetch_all($result);

                $elenco_parenti = [];
                foreach ($parenti_tmp as $parente) {
                    foreach ($parente as $key =>  $value) {
                        $parente[$key] = decode($value);
                    }

                    $elenco_parenti[$parente['id_parente']] = $parente;
                }

                //parenti_studenti
                $query = "SELECT *
                    FROM parenti_studenti
                    WHERE flag_canc = 0 AND {$where_parenti_studenti}";

                $result = pgsql_query($query) or die("Invalid $query");
                $numero = pg_num_rows($result);
                $parenti_studenti_tmp = pg_fetch_all($result);

                $abbinamenti = [];
                foreach ($parenti_studenti_tmp as $abb){
                    $abbinamenti['parenti_studenti'][$abb['id_parente']][$abb['id_studente']] = $abb;
                    $abbinamenti['studenti_parenti'][$abb['id_studente']][$abb['id_parente']] = $abb;
                }
                break;
            /* ------------------------------------------------------------------------------------------------------------------------- */
            default:
                break;
        }
    }

    foreach ($id_oggetti as $id_oggetto){
        $el_tmp = [];

        switch ($tipo_oggetti_input) {
            case 'STUDENTI':
                $chiave = 'id_studente';
                $tipo_oggetto = 'S';

                foreach ($variabili_template as $variabile) {
                    $x = explode('_', $variabile);

                    if (preg_match("/tab\d+/", $x[0])) {
                        // pulisco le variabili con tab1_ ecc, tipo tab1_nome_studente diventa nome_studente
                        $variabile = substr($variabile, strlen($x[0]) + 1);
                    }

                    switch ($variabile) {
                        case 'data_oggi':
                            $today = getdate();
                            $el_tmp[$variabile] = $today['mday'] . '/' . $today['mon'] . '/' . $today['year'];
                            break;

                        case 'anno_scolastico_attuale':
                            $el_tmp[$variabile] = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
                            break;

                        case 'descrizione_indirizzi_studente':
                        case 'classe_studente':
                        case 'sezione_studente':
                            $campo = str_replace('_studente', '', $variabile);
                            if (isset($elenco_studenti[$id_oggetto]['classi'])){
                                foreach ($elenco_studenti[$id_oggetto]['classi'] as $classe){
                                    if ($classe['ordinamento'] == 0){
                                        $el_tmp[$variabile] = $classe[$campo];
                                    }
                                }
                            }
                            break;
                        case 'data_nascita_studente':
                        case 'data_ritiro_studente':
                        case 'data_arrivo_in_italia_studente':
                        case 'data_iscrizione_studente':
                        case 'data_nascita_padre':
                        case 'data_nascita_madre':
                        case 'data_nascita_tutore':
                            $campo = str_replace('_studente', '', $variabile);
                            if ( !empty($elenco_studenti[$id_oggetto][$campo])
                                    &&
                                $elenco_studenti[$id_oggetto][$campo] != 0 )
                            {
                                $el_tmp[$variabile] = date("d/m/Y", intval($elenco_studenti[$id_oggetto][$campo]));
                            }
                            else {
                                $el_tmp[$variabile] = '';
                            }
                            break;
                        case 'esonero_ed_fisica_studente':
                        case 'stage_professionali_studente':
                        case 'acconsente_aziende_studente':
                        case 'necessita_alfabetizzazione_studente':
                        case 'qualifica_iefp_studente':
                        case 'acconsente_studente':
                        case 'servizio_mensa_studente':
                        case 'adottato_studente':
                        case 'vaccinazioni_studente':
                        case 'necessita_sostegno_studente':
                        case 'autocertificazione_vaccinazioni_studente':
                        case 'sostenuto_prove_invalsi_medie_studente':
                        case 'esonero_religione_studente':
                        case 'frequenza_asilo_nido_studente':
                        case 'frequenza_scuola_materna_studente':
                            $campo = str_replace('_studente', '', $variabile);
                            switch ($elenco_studenti[$id_oggetto][$campo]) {
                                case 0:
                                    $el_tmp[$variabile] = 'NO';
                                    break;
                                case 1:
                                    $el_tmp[$variabile] = 'SI';
                                    break;
                                default:
                                    $el_tmp[$variabile] = 'non disponibile';
                                    break;
                            }
                            break;
                        case 'grado_handicap_studente':
                            $campo = str_replace('_studente', '', $variabile);
                            switch ($elenco_studenti[$id_oggetto][$campo]) {
                                case 0:
                                    $el_tmp[$variabile] = 'nessun handicap';
                                    break;
                                case 1:
                                    $el_tmp[$variabile] = 'grado 1';
                                    break;
                                case 2:
                                    $el_tmp[$variabile] = 'grado 2';
                                    break;
                                case 3:
                                    $el_tmp[$variabile] = 'grado 3';
                                    break;
                                case 4:
                                    $el_tmp[$variabile] = 'grado 4';
                                    break;
                            }
                            break;
                        case 'consiglio_orientativo_trentino_studente':
                            $consiglio_orientativo = [];
                            $valori_consiglio_orientativo = estrai_valori_consiglio_orientativo_trentino($elenco_studenti[$id_oggetto]['consiglio_orientativo_trentino']);
                            foreach ($valori_consiglio_orientativo as $valore)
                            {
                                $consiglio_orientativo[] = $valore['descrizione'];
                            }
                            $consiglio_orientativo = implode(', ', $consiglio_orientativo);
                            $el_tmp['consiglio_orientativo_trentino_studente'] = "Consiglio Orientativo: " . $consiglio_orientativo;
                            break;
                        case 'pagante_padre':
                        case 'pagante_madre':
                        case 'pagante_tutore':
                            $el_tmp[$variabile] == true ? 'Pagante': 'Non pagante';
                            break;
                        case 'seconda_materia_sostitutiva_religione_studente':
                        case 'materia_sostitutiva_religione_studente':
                        case 'materia_sostitutiva_edfisica_studente':
                            $campo = str_replace('_studente', '', $variabile);
                            $dati_materia = [];
                            if ($elenco_studenti[$id_oggetto][$campo] > 0) {
                                $dati_materia = estrai_dati_materia($elenco_studenti[$id_oggetto][$campo]);
                            }
                            $el_tmp[$variabile] = $dati_materia['descrizione'];
                            break;
                        case 'nome_completo_studente':
                            $el_tmp['nome_completo_studente'] = $elenco_studenti[$id_oggetto]['cognome'] . ' ' . $elenco_studenti[$id_oggetto]['nome'];
                            break;
                        case 'nome_completo_padre':
                            $el_tmp['nome_completo_padre'] = $elenco_studenti[$id_oggetto]['cognome_padre'] . ' ' . $elenco_studenti[$id_oggetto]['nome_padre'];
                            break;
                        case 'nome_completo_madre':
                            $el_tmp['nome_completo_madre'] = $elenco_studenti[$id_oggetto]['cognome_madre'] . ' ' . $elenco_studenti[$id_oggetto]['nome_madre'];
                            break;
                        case 'nome_completo_tutore':
                            $el_tmp['nome_completo_tutore'] = $elenco_studenti[$id_oggetto]['cognome_tutore'] . ' ' . $elenco_studenti[$id_oggetto]['nome_tutore'];
                            break;
                        case 'classe_completa_studente':
                            $el_tmp['classe_completa_studente'] = $elenco_studenti[$id_oggetto]['classe'] . $elenco_studenti[$id_oggetto]['sezione'] . ' ' . $elenco_studenti[$id_oggetto]['descrizione_indirizzi'];
                            break;
                        case 'nascita_completa_studente':
                            $el_tmp['nascita_completa_studente'] = $elenco_studenti[$id_oggetto]['descrizione_nascita'] . ' (' . $elenco_studenti[$id_oggetto]['provincia_nascita_da_comune'] . ')';
                            break;
                        case 'residenza_completa_studente':
                            $el_tmp['residenza_completa_studente'] = $elenco_studenti[$id_oggetto]['descrizione_residenza'] . ' (' . $elenco_studenti[$id_oggetto]['provincia_residenza_da_comune'] . ')';
                            break;
                        case 'nomi_genitori':
                            $parentele = ['P', 'M'];
                            $genitori = [];

                            $el_tmp['nomi_genitori'] = "";

                            if (isset($abbinamenti['studenti_parenti'][$id_oggetto])){
                                foreach ($abbinamenti['studenti_parenti'][$id_oggetto] as $abbinamento){
                                    if (in_array($abbinamento['parentela'], $parentele) && isset($elenco_parenti[$abbinamento['id_parente']])){
                                        $genitori[] = $elenco_parenti[$abbinamento['id_parente']]['cognome'] . ' ' . $elenco_parenti[$abbinamento['id_parente']]['nome'] . ' (' . traduci_parentela($abbinamento['parentela']) . ')';
                                    }
                                }

                                if (count($genitori) > 0){
                                    $el_tmp['nomi_genitori'] = implode(', ', $genitori);
                                }
                            }
                            break;
                        case 'nomi_parenti':
                            $parenti = [];

                            $el_tmp['nomi_parenti'] = "";

                            if (isset($abbinamenti['studenti_parenti'][$id_oggetto])){
                                foreach ($abbinamenti['studenti_parenti'][$id_oggetto] as $abbinamento){
                                    if (isset($elenco_parenti[$abbinamento['id_parente']])){
                                        $parenti[] = $elenco_parenti[$abbinamento['id_parente']]['cognome'] . ' ' . $elenco_parenti[$abbinamento['id_parente']]['nome'] . ' (' . traduci_parentela($abbinamento['parentela']) . ')';
                                    }
                                }

                                if (count($parenti) > 0){
                                    $el_tmp['nomi_parenti'] = implode(', ', $parenti);
                                }
                            }
                        case 'tabella_parenti_studente':
                            $parenti = [];

                            $el_tmp['tabella_parenti_studente'] = [];

                            if (isset($abbinamenti['studenti_parenti'][$id_oggetto])){
                                foreach ($abbinamenti['studenti_parenti'][$id_oggetto] as $abbinamento){
                                    if (isset($elenco_parenti[$abbinamento['id_parente']])){
                                        $parente = [];
                                        $parente['cognome']['valore'] = $elenco_parenti[$abbinamento['id_parente']]['cognome'];
                                        $parente['nome']['valore'] = $elenco_parenti[$abbinamento['id_parente']]['nome'];
                                        $parente['parentela']['valore'] = traduci_parentela($abbinamento['parentela']);

                                        foreach ($parente as $nome_campo => $campo) {
                                            if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])) {
                                                $parente[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                            }
                                        }

                                        $parenti[] = $parente;
                                    }
                                }

                                if (count($parenti) > 0){
                                    $el_tmp['tabella_parenti_studente'] = $parenti;
                                }
                            }
                            break;
                        case 'tabella_classi_studente':
                            $classi = [];

                            $el_tmp['tabella_classi_studente'] = [];

                            if (isset($elenco_studenti[$id_oggetto]['classi'])){
                                foreach ($elenco_studenti[$id_oggetto]['classi'] as $classe){
                                    if ($classe['ordinamento'] != 'CORSO'){
                                        $classe_tmp = [];
                                        $classe_tmp['classe']['valore'] = $classe['classe'];
                                        $classe_tmp['sezione']['valore'] = $classe['sezione'];
                                        $classe_tmp['descrizione_indirizzi']['valore'] = $classe['descrizione_indirizzi'];

                                        foreach ($classe_tmp as $nome_campo => $campo){
                                            if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])){
                                                $classe_tmp[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                            }
                                        }

                                        $classi[] = $classe_tmp;
                                    }
                                }

                                if (count($classi) > 0){
                                    $el_tmp['tabella_classi_studente'] = $classi;
                                }
                            }
                            break;
                        case 'tabella_corsi_studente':
                            $corsi = [];

                            $el_tmp['tabella_corsi_studente'] = [];

                            if (isset($elenco_studenti[$id_oggetto]['classi'])){
                                foreach ($elenco_studenti[$id_oggetto]['classi'] as $classe){
                                    if ($classe['ordinamento'] == 'CORSO'){
                                        $corso_tmp = [];
                                        $corso_tmp['descrizione']['valore'] = $classe['sezione'];

                                        foreach ($corso_tmp as $nome_campo => $campo) {
                                            if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])) {
                                                $corso_tmp[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                            }
                                        }

                                        $corsi[] = $corso_tmp;
                                    }
                                }

                                if (count($corsi) > 0){
                                    $el_tmp['tabella_corsi_studente'] = $corsi;
                                }
                            }
                            break;

                        case 'tabella_movimenti_studente':
                            $movimenti = [];

                            $el_tmp['tabella_movimenti_studente'] = [];

                            if (isset($elenco_studenti[$id_oggetto]['movimenti_studente'])){

                                foreach ($elenco_studenti[$id_oggetto]['movimenti_studente'] as $movimento){
                                    $movimento_tmp = [];
                                    $movimento_tmp['data_scadenza']['valore'] = $movimento['data_scadenza_tradotta'];
                                    $movimento_tmp['amount']['valore'] = number_format($movimento['amount'], 2, '.', ' ')."&#8364;";
                                    $movimento_tmp['descrizione']['valore'] = $movimento['movement_description'];
//                                    $movimento_tmp['importo_pagato']['valore'] =
//                                            (
//                                                $movimento['remaining'] == 0 ?
//                                                    'PAGATO' : 'DA PAGARE'
//                                            );

                                    foreach ($movimento_tmp as $nome_campo => $campo){
                                        if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])){
                                            $movimento_tmp[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                        }
                                    }

                                    $movimenti['dati'][] = $movimento_tmp;
                                }

                                if (count($movimenti['dati']) > 0){
                                    $movimenti['opzioni']['colore_bordo_tabella'] = null;
                                    $movimenti['opzioni']['dimensione_bordo_tabella'] = null;
                                    $el_tmp['tabella_movimenti_studente'] = $movimenti;
                                }
                            }
                            break;

                        case 'tabella_scuole_frequentate_studente':
                            $curriculum_scuole_frequentate = [];

                            $el_tmp['tabella_scuole_frequentate_studente'] = [];

                            $curriculum_intestaz = [];
                            $curriculum_intestaz['anno_scolastico']['valore'] = 'Anno Scolastico';
                            $curriculum_intestaz['descrizione_scuola']['valore'] = 'Intitolazione Scuola';
                            $curriculum_intestaz['non_statale']['valore'] = 'Non statale';

                            foreach ($curriculum_intestaz as $nome_campo => $campo){
                                if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])){
                                    $curriculum_intestaz[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                }
                            }

                            $curriculum_scuole_frequentate[] = $curriculum_intestaz;

                            $studente_curriculum = $elenco_studenti[$id_oggetto]['dati_curriculum'];
                            if (isset($studente_curriculum)) {
                                foreach ($studente_curriculum as $cont => $singolo_curriculum) {

                                    // tolgo le righe di iscrizione
                                    if ($singolo_curriculum['esito'] == 'Iscritto') {
                                        continue;
                                    }
                                    $curriculum_tmp = [];
                                    $curriculum_tmp['anno_scolastico']['valore'] = $singolo_curriculum['anno_scolastico'];
                                    $classe_tradotta = '';
                                    switch ($singolo_curriculum['classe']) {
                                        case '-3':
                                        case '1':
                                        case '6':
                                        case '9':
                                        case '31':
                                            $classe_tradotta = '1';
                                            break;
                                        case '-2':
                                        case '2':
                                        case '7':
                                        case '10':
                                        case '32':
                                            $classe_tradotta = '2';
                                            break;
                                        case '-1':
                                        case '3':
                                        case '8':
                                        case '11':
                                        case '33':
                                            $classe_tradotta = '3';
                                            break;
                                        case '4':
                                        case '12':
                                        case '29':
                                            $classe_tradotta = '4';
                                            break;
                                        case '5':
                                        case '30':
                                        case '13':
                                            $classe_tradotta = '5';
                                            break;
                                        default:
                                            $classe_tradotta = $singolo_curriculum['classe'];
                                            break;
                                    }
                                    $curriculum_tmp['descrizione_scuola']['valore'] = $classe_tradotta . $singolo_curriculum['descrizione'] . ' ' . $singolo_curriculum['descrizione_libera_scuola'];
                                    $curriculum_tmp['non_statale']['valore'] = '';

                                    foreach ($curriculum_tmp as $nome_campo => $campo){
                                        if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])){
                                            $curriculum_tmp[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                        }
                                    }

                                    $curriculum_scuole_frequentate[] = $curriculum_tmp;
                                }

                                if (count($curriculum_scuole_frequentate) > 0){
                                    $el_tmp['tabella_scuole_frequentate_studente'] = $curriculum_scuole_frequentate;
                                }
                            }
                            break;

                        case 'tabella_curriculum_completo_studente':
                            $curriculum_completo_studente = [];

                            $el_tmp['tabella_curriculum_completo_studente'] = [];

                            $curriculum_intestaz = [];
                            $curriculum_intestaz['anno_scolastico']['valore'] = 'ANNO SCOLASTICO';
                            $curriculum_intestaz['classe']['valore'] = 'CLASSE';
                            $curriculum_intestaz['ant']['valore'] = 'ANT';
                            $curriculum_intestaz['reg']['valore'] = 'REG';
                            $curriculum_intestaz['rit']['valore'] = 'RIT';
                            $curriculum_intestaz['anni']['valore'] = 'ANNI';
                            $curriculum_intestaz['ripetente']['valore'] = 'RIPETENTE';
                            $curriculum_intestaz['data_iscrizione']['valore'] = 'DATA ISCRIZIONE';
                            $curriculum_intestaz['interruzione_frequenza']['valore'] = 'INTERRUZIONE  FREQUENZA IN CORSO D\'A.S.';
                            $curriculum_intestaz['risultato']['valore'] = 'RISULTATO FINALE';

                            foreach ($curriculum_intestaz as $nome_campo => $campo){
                                if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])){
                                    $curriculum_intestaz[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                }
                            }

                            $curriculum_completo_studente[] = $curriculum_intestaz;

                            $studente_curriculum = $elenco_studenti[$id_oggetto]['dati_curriculum'];
                            if (isset($studente_curriculum)) {
                                foreach ($studente_curriculum as $cont => $singolo_curriculum) {

                                    $anno_scolastico = $singolo_curriculum['anno_scolastico'];
                                    $esito = decode($singolo_curriculum['esito']);

                                    // tolgo le righe di iscrizione
                                    if ($esito == 'Iscritto'){
                                        continue;
                                    }

                                    //{{{ <editor-fold defaultstate="collapsed" desc="curriculum">
                                    switch ($singolo_curriculum['tipo_scuola']) {
                                        case "scuola_media":
                                            $classe_frequentata = "Scuola media: " . $singolo_curriculum['nome_scuola'];
                                            break;
                                        case "scuola_superiore":
                                            if ($singolo_curriculum['classe'] == 0) {
                                                $classe_frequentata = "Istituto: " . $singolo_curriculum['nome_scuola'];
                                            } else {
                                                $classe_frequentata = "Classe: " . $singolo_curriculum['classe'] .
                                                        " " .
                                                        "Istituto: " . $singolo_curriculum['nome_scuola'];
                                            }
                                            break;
                                        case "classe_interna":
                                            if ($singolo_curriculum['classe'] == 0) {
                                                $classe_frequentata = $singolo_curriculum['sezione'] .
                                                        " " .
                                                        $singolo_curriculum['indirizzo'];
                                            } else {
                                                $classe_frequentata = $singolo_curriculum['classe'] .
                                                        " " .
                                                        $singolo_curriculum['sezione'] .
                                                        " " .
                                                        $singolo_curriculum['indirizzo'];
                                            }
                                            break;
                                        case "altro":
                                            $classe_frequentata = $singolo_curriculum['altro'];
                                            break;
                                        default:
                                            if ($singolo_curriculum['classe'] == 0) {
                                                $classe_frequentata = $singolo_curriculum['altro'];
                                            } else {
                                                if (strlen($singolo_curriculum['altro']) > 0) {
                                                    $classe_frequentata = $singolo_curriculum['altro'] . " ";
                                                } else {
                                                    $classe_frequentata = "";
                                                }

                                                $classe_frequentata .= $singolo_curriculum['classe'] .
                                                        " " .
                                                        $singolo_curriculum['sezione'] .
                                                        " " .
                                                        $singolo_curriculum['indirizzo'];
                                            }
                                            break;
                                    }
                                    //}}} </editor-fold>
                                    $stampa_ripetente = '';
                                    $esito_compatto = '';
                                    switch ($esito) {
                                        case 'Ammesso alla classe successiva':
                                        case 'Ammesso al successivo grado dell\'istruzione obbligatoria':
                                        case 'Qualificato':
                                        case 'Licenziato':
                                        case 'Diplomato':
                                            $esito_compatto = 'am';
                                            break;
                                        case 'Non ammesso alla classe successiva':
                                        case 'Non ammesso al successivo grado dell\'istruzione obbligatoria':
                                        case 'Non ammesso esame di stato':
                                        case 'Non qualificato':
                                        case 'Non diplomato':
                                            $esito_compatto = 'n/a';
                                            $ripetente = explode('/',$anno_scolastico)[1] . "/" . (explode('/',$anno_scolastico)[1]  +1);
                                            break;
                                        case 'Iscritto':
                                            $esito_compatto = 'isc';
                                            break;
                                        case 'Ammesso esame di stato':
                                            $esito_compatto = 'am_e';
                                            break;
                                        case 'Trasferito':
                                            $esito_compatto = 'tra';
                                            break;
                                        case 'Ritirato':
                                            $esito_compatto = 'rit';
                                            break;
                                        default:
                                            $esito_compatto = '';
                                            break;
                                    }
                                    if ($studente['codice_meccanografico'] != $singolo_curriculum['id_scuola'] && $studente['codice_meccanografico_secondario'] != $singolo_curriculum['id_scuola']) {
                                        $classe_frequentata = $singolo_curriculum['classe_desc'] . ' ' . $singolo_curriculum['nome_scuola'];
                                    } else {
                                        $classe_frequentata = $singolo_curriculum['classe_tradotta'] . ' ' . $singolo_curriculum['descrizione'];
                                    }

                                    if ($ripetente == $anno_scolastico) {
                                        $stampa_ripetente = 'SI';
                                    }

                                    $curriculum_tmp = [];
                                    $curriculum_tmp['anno_scolastico']['valore'] = $anno_scolastico;
                                    $curriculum_tmp['classe']['valore'] = $classe_frequentata;
                                    $curriculum_tmp['ant']['valore'] = '';
                                    $curriculum_tmp['reg']['valore'] = '';
                                    $curriculum_tmp['rit']['valore'] = '';
                                    $curriculum_tmp['anni']['valore'] = '';
                                    $curriculum_tmp['ripetente']['valore'] = $stampa_ripetente;
                                    $curriculum_tmp['data_iscrizione']['valore'] = '';
                                    $curriculum_tmp['interruzione_frequenza']['valore'] = '';
                                    $curriculum_tmp['risultato']['valore'] = $esito;

                                    foreach ($curriculum_tmp as $nome_campo => $campo){
                                        if (isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'])){
                                            $curriculum_tmp[$nome_campo]['width'] = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['colonne'][$nome_campo]['width'];
                                        }
                                    }

                                    $curriculum_completo_studente[] = $curriculum_tmp;
                                }

                                if (count($curriculum_completo_studente) > 0){
                                    $el_tmp['tabella_curriculum_completo_studente'] = $curriculum_completo_studente;
                                }
                            }
                            break;

                        case 'giudizio_sintetico_esame_terza_media_lettere_studente':
                            $el_tmp['giudizio_sintetico_esame_terza_media_lettere_studente'] = traduci_numero_in_lettere($elenco_studenti[$id_oggetto]['giudizio_sintetico_esame_terza_media']);
                            break;

                        case 'esame_terza_media_presidente_commissione':
                            $nome_presidente = '';
                            foreach($elenco_studenti[$id_oggetto]['esame_medie_commissione'] as $commissione)
                            {
                                if($commissione['id_classe'] == $commissione['id_classe_commissione_medie'])
                                {
                                    $abbinamenti_commissari = estrai_abbinamenti_commissari($commissione['id_commissione']);
                                    foreach($abbinamenti_commissari['esterni'] as $singolo_abbinamento)
                                    {
                                        if($singolo_abbinamento['dati_commissario']['ruolo_commissario_est'] == 'P')
                                        {
                                            $nome_presidente = $singolo_abbinamento['nome']
                                                               . ' ' .
                                                               $singolo_abbinamento['cognome'];
                                        }
                                    }
                                }
                            }
                            $el_tmp['esame_terza_media_presidente_commissione'] = $nome_presidente;
                            break;

                        case 'provincia_nascita_studente':
                            $el_tmp['provincia_nascita_studente'] = $elenco_studenti[$id_oggetto]['provincia_nascita'] != '' ? $elenco_studenti[$id_oggetto]['provincia_nascita'] : $elenco_studenti[$id_oggetto]['provincia_nascita_da_comune'];
                            break;

                        case 'cognome_pagante':
                        case 'nome_pagante':
                        case 'data_nascita_pagante':
                        case 'codice_fiscale_pagante':
                        case 'cittadinanza_pagante':
                        case 'utente_pagante':
                        case 'luogo_nascita_pagante':
                        case 'provincia_nascita_pagante':
                        case 'stato_nascita_pagante':
                        case 'indirizzo_residenza_pagante':
                        case 'cap_residenza_pagante':
                        case 'citta_residenza_pagante':
                        case 'provincia_residenza_pagante':
                        case 'telefono_residenza_pagante':
                        case 'telefono_cellulare_pagante':
                        case 'email_pagante':
                        case 'citta_straniera_nascita_pagante':
                        case 'iban_pagante':
                            $dati_pagante = [];
                            $el_tmp[$variabile] = "";
                            $variabile_campo = str_replace('_pagante', '', $variabile);

                            if (isset($abbinamenti['studenti_parenti'][$id_oggetto])){
                                foreach ($abbinamenti['studenti_parenti'][$id_oggetto] as $abbinamento){

                                    if (isset($elenco_parenti[$abbinamento['id_parente']])
                                            &&
                                        $abbinamento['pagante']){

                                        $dati_pagante = $elenco_parenti[$abbinamento['id_parente']];
                                        $dati_pagante['abbin'] = $abbinamento;
                                    }
                                }

                                if ( isset($dati_pagante) ) {
                                    $valore_campo = '';
                                    switch ($variabile_campo)
                                    {
                                        case 'stato_nascita':
                                            $valore_campo = estrai_nazione($dati_pagante['id_stato_nascita'])['descrizione'];
                                            break;

                                        case 'cittadinanza':
                                            $dati_nazione_citt = estrai_nazione($dati_pagante['id_stato_nascita']);
                                            if($dati_nazione_citt[0] != "ERROR")
                                            {
                                                $valore_campo = $dati_nazione_citt["descrizione"];
                                            }
                                            break;

                                        case 'luogo_nascita':
                                        case 'provincia_nascita':
                                        case 'stato_nascita':
                                            $valore_campo = '';
                                            $comune_nasc = estrai_provincia_comune(str_replace("'", "", $dati_pagante["id_luogo_nascita"]));

                                            if ($comune_nasc[0] != "ERROR") {
                                                $valore_campo = $comune_nasc[$variabile_campo];

                                                if ($variabile_campo == 'luogo_nascita') {
                                                    $valore_campo = $comune_nasc['descrizione'];
                                                }
                                                elseif ($variabile_campo == 'provincia_nascita') {
                                                    $valore_campo = $comune_nasc['codice'];
                                                }
                                            }
                                            break;

                                        case 'citta_residenza':
                                        case 'provincia_residenza':
                                            $valore_campo = '';
                                            $comune_resid = estrai_provincia_comune($dati_pagante["id_luogo_residenza"]);

                                            if ($comune_resid[0] != "ERROR") {
                                                $valore_campo = $comune_resid[$variabile_campo];

                                                if ($variabile_campo == 'citta_residenza') {
                                                    $valore_campo = $comune_resid['descrizione'];
                                                }
                                                elseif ($variabile_campo == 'provincia_residenza') {
                                                    $valore_campo = $comune_resid['codice'];
                                                }
                                            }
                                            break;


                                        case 'iban':
                                            $valore_campo = $dati_pagante['abbin'][$variabile_campo];
                                            break;

                                        case 'indirizzo_residenza':
                                            $valore_campo = $dati_pagante['indirizzo'];
                                            break;

                                        case 'data_nascita':
                                            if ( !empty($dati_pagante[$variabile_campo])
                                                    &&
                                                $dati_pagante[$variabile_campo] != 0 )
                                            {
                                                $valore_campo = date("d/m/Y", intval($dati_pagante[$variabile_campo]));
                                            }
                                            else {
                                                $valore_campo = '';
                                            }
                                            break;

                                        default:
                                            $valore_campo = $dati_pagante[$variabile_campo];
                                    }

                                    $el_tmp[$variabile] = $valore_campo;
                                }
                            }
                            break;

                        case 'nome_dirigente_studente':
                            $nome_campo = str_replace('_studente', '', $variabile);
                            foreach ($elenco_studenti[$id_oggetto]['classi'] as $id_cl => $classe_stud) {
                                if ($classe_stud['ordinamento'] == '0') {
                                    $dati_classe = estrai_classe($id_cl);
                                    $el_tmp[$variabile] =  $dati_classe[$nome_campo];
                                }
                            }
                            break;

                        case 'suffisso_o_a':
                        case 'suffisso_e_essa':
                        case 'suffisso_O_A':
                        case 'suffisso_E_ESSA':
                            if ($elenco_studenti[$id_oggetto]['sesso'] == 'M') {
                                $el_tmp['suffisso_o_a'] = 'o';
                                $el_tmp['suffisso_e_essa'] = 'e';
                                $el_tmp['suffisso_O_A'] = 'O';
                                $el_tmp['suffisso_E_ESSA'] = 'E';
                            }
                            else {
                                $el_tmp['suffisso_o_a'] = 'a';
                                $el_tmp['suffisso_e_essa'] = 'essa';
                                $el_tmp['suffisso_O_A'] = 'A';
                                $el_tmp['suffisso_E_ESSA'] = 'ESSA';
                            }
                            break;

                        default:
                            if ( array_key_exists($variabile, $corrispondenze_campi_tabelle) )
                            {
                                $nome_campo = str_replace('_studente', '', $variabile);

                                if (isset($elenco_studenti[$id_oggetto][$nome_campo])) {
                                    $el_tmp[$variabile] = $elenco_studenti[$id_oggetto][$nome_campo];
                                }
                            }
                            else {
                                $el_tmp[$variabile] = '{'.$variabile.'}';
                            }

                            // dati_personalizzati
                            if ( stripos($variabile, 'dati_personalizzati_') !== false ) {
                                $el_tmp[$variabile] = '';
                                $variabile_nome = str_replace('dati_personalizzati_', '', $variabile);
                                $dato_pers = $elenco_studenti[$id_oggetto][$variabile_nome];

                                if ( isset($corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']) ) {
                                    $descrizione_stampa = $dato_pers['descrizione'];
                                    $valore_stud = ( $dato_pers['valore'] != '' ? $dato_pers['valore'] : "0" );
                                    $valore_stampa = $corrispondenze_campi_tabelle[$variabile]['dati_aggiuntivi']['valori'][$valore_stud];
                                    $el_tmp[$variabile] = $valore_stampa.' - '.$descrizione_stampa;
                                }
                                else {
                                    $el_tmp[$variabile] = $dato_pers['valore'];
                                }
                            }
                            break;
                    }
                }

                break;
            case 'PARENTI':
                $chiave = 'id_parente';
                $tipo_oggetto = 'P';
                break;
            default:
                break;
        }
        $el_tmp['tipo'] = $tipo_oggetto;
        $el_tmp['chiave'] = $chiave;
        $el_tmp[$chiave] = $id_oggetto;

        switch ($tipo_template) {
            case 'singolo':
                $return[$id_oggetto] = $el_tmp;
                break;
            case 'gruppo':
                $return[$id_oggetto] = $el_tmp;
                break;
            default:
                break;
        }
    }
    return $return;
}
function estrai_modelli_word($id_template = null, $locazione = null, $flag_canc = '') {
    $elenco = [];
    $filtro_id_template = !$id_template ? "" : "AND id_template = '{$id_template}'";
    $filtro_locazione = !$locazione ? "" : "AND locazione = '{$locazione}' ";
    $filtro_flag_canc = empty($flag_canc) ? "flag_canc = 0" : " flag_canc $flag_canc ";

    $sql = "SELECT *
            FROM template_word
            WHERE $filtro_flag_canc
            $filtro_id_template
            $filtro_locazione
            ORDER BY ordinamento
    ";
    $result = pgsql_query($sql) or die("Invalid $sql");

    foreach (pg_fetch_all($result) as $template)
    {
        $template['parametri_personalizzati'] = json_decode($template['parametri_personalizzati'], true);
        $elenco[] = $template;
    }

    return $elenco;
}

function estrai_abbinamenti_modello_word($id_template, $id_classe='', $periodo='', $tipo_abbinamento='')
{
    $abbinamenti = [];

    $where_classe = ( $id_classe != '' ? " AND id_classe = $id_classe " : '' );
    $where_periodo = ( $periodo != '' ? " AND periodo = '$periodo' " : '' );
    $where_abbinamento = ( $tipo_abbinamento != '' ? " AND tipo_abbinamento = $tipo_abbinamento " : '' );

    $sql = "SELECT *
            FROM abbinamenti_template_word_pagelle_classi
            WHERE id_template = '{$id_template}'
                $where_classe
                $where_periodo
                $where_abbinamento
    ";
    $result = pgsql_query($sql) or die("Invalid $sql");

    foreach (pg_fetch_all($result) as $abbinamento)
    {
       $abbinamenti[] = $abbinamento;
    }

    return $abbinamenti;
}
function estrai_abbinamenti_modelli_word_classe($id_classe, $id_template='', $periodo='', $tipo_abbinamento='')
{
    $abbinamenti = [];

    $where_template = ( $id_template != '' ? " AND id_template = '$id_template' " : '' );
    $where_periodo = ( $periodo != '' ? " AND periodo = '$periodo' " : '' );
    $where_abbinamento = ( $tipo_abbinamento != '' ? " AND tipo_abbinamento = $tipo_abbinamento " : '' );

    $sql = "SELECT *
            FROM abbinamenti_template_word_pagelle_classi
            WHERE id_classe = $id_classe
                $where_template 
                $where_periodo
                $where_abbinamento
    ";
    $result = pgsql_query($sql) or die("Invalid $sql");

    foreach (pg_fetch_all($result) as $abbinamento)
    {
       $abbinamenti[] = $abbinamento;
    }

    return $abbinamenti;
}

function template_word_pagelle_lista_parametri_personalizzati()
{
    return array(
        array(
            "nome" => "frase_aggiuntiva",
            "label" => "Testo libero",
            "tipo" => "text"
        ),
        array(
            "nome" => "frase_aggiuntiva_1",
            "label" => "Testo libero 1",
            "tipo" => "text"
        ),
        array(
            "nome" => "data_stampa_",
            "label" => "Data di stampa",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_1qt_",
            "label" => "Data di stampa primo quadrimestre/trimestre",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_2t_",
            "label" => "Data di stampa 2 trimestre",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_finale_",
            "label" => "Data di stampa finale",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_pagellina1_",
            "label" => "Data di stampa del primo periodo infraquadrimestrale",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_pagellina2_",
            "label" => "Data di stampa del secondo periodo infraquadrimestrale",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_pagellina3_",
            "label" => "Data di stampa del terzo periodo infraquadrimestrale",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_pagellina4_",
            "label" => "Data di stampa del quarto periodo infraquadrimestrale",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_stampa_sospesi_",
            "label" => "Data di stampa per i sospesi",
            "tipo" => "date"
        ),
        array(
            "nome" => "data_calcolo_",
            "label" => "Intervallo date entro cui calcolare assenze e note",
            "tipo" => "multi-date",
            "inputs" => array(
                array(
                    "nome" => "data_calcolo_inizio_",
                    "tipo" => "date"
                ),
                array(
                    "nome" => "data_calcolo_fine_",
                    "tipo" => "date"
                )
            )
        ),
        array(
            "nome" => "tipo_dirigente",
            "label" => "Dirigente",
            "tipo" => "select",
            "valori" => array(
                "default" => "Dott.",
                "opzioni" => array(
                    array(
                        "label" => "Dirigente",
                        "valore" => "Dirigente"
                    ),
                    array(
                        "label" => "Dott.",
                        "valore" => "Dott."
                    )
                )
            )
        ),
        array(
            "nome" => "tipologia_scuola",
            "label" => "Tipologia della scuola",
            "tipo" => "select",
            "valori" => array(
                "default" => "Statale",
                "opzioni" => array(
                    array(
                        "label" => "Statale",
                        "valore" => "Statale"
                    ),
                    array(
                        "label" => "Paritaria",
                        "valore" => "Paritaria"
                    )
                )
            )
        ),
        array(
            "nome" => "stampa_competenze_trasversali",
            "label" => "Stampare le competenze trasversali",
            "tipo" => "select",
            "valori" => array(
                "default" => "NO",
                "opzioni" => array(
                    array(
                        "label" => "No",
                        "valore" => "NO"
                    ),
                    array(
                        "label" => "Si",
                        "valore" => "SI"
                    )
                )
            )
        ),
        array(
            "nome" => "quali_competenze_stampare",
            "label" => "Quali competenze stampare",
            "tipo" => "select",
            "valori" => array(
                "default" => "SOLO_COMPETENZE_CON_VOTI",
                "opzioni" => array(
                    array(
                        "label" => "Solo competenze compilate",
                        "valore" => "SOLO_COMPETENZE_CON_VOTI"
                    ),
                    array(
                        "label" => "Tutte",
                        "valore" => "TUTTE"
                    )
                )
            )
        ),
        array(
            "nome" => "filtro_studenti_scrutinio",
            "label" => "Filtro gli studenti per scrutinio",
            "tipo" => "select",
            "valori" => array(
                "default" => "TUTTI",
                "opzioni" => array(
                    array(
                        "label" => "Tutti",
                        "valore" => "TUTTI"
                    ),
                    array(
                        "label" => "Almeno un voto negativo",
                        "valore" => "NEGATIVO"
                    )
                )
            )
        ),
        array(
            "nome" => "filtro_materie",
            "label" => "Quali materie stampare",
            "tipo" => "select",
            "valori" => array(
                "default" => "NO_FILTRO",
                "opzioni" => array(
                    array(
                        "label" => "Tutte",
                        "valore" => "NO_FILTRO"
                    ),
                    array(
                        "label" => "Solo con voti/competenze",
                        "valore" => "SOLO_VOTI"
                    )
                )
            ),
            "selected" => true
        ),
        array(
            "nome" => "filtro_giudizio_sospeso",
            "label" => "Filtro degli studenti sospesi",
            "tipo" => "select",
            "valori" => array(
                "default" => "TUTTI",
                "opzioni" => array(
                    array(
                        "label" => "Di tutti",
                        "valore" => "TUTTI"
                    ),
                    array(
                        "label" => "Solo di quelli che hanno risolto il giudizio sospeso che sono stati poi promossi",
                        "valore" => "SOSPESI_PROMOSSI"
                    ),
                    array(
                        "label" => "Solo di quelli che hanno risolto il giudizio sospeso che non sono stati poi promossi",
                        "valore" => "SOSPESI_BOCCIATI"
                    ),
                    array(
                        "label" => "Solo di quelli che NON hanno avuto il giudizio sospeso",
                        "valore" => "NON_SOSPESI"
                    ),
                    array(
                        "label" => "Solo di quelli che HANNO il giudizio in sospeso",
                        "valore" => "SOLO_SOSPESI"
                    )
                )
            ),
            "selected" => false
        ),
        array(
            "nome" => "quanti_studenti_stampare",
            "label" => "Quanti studenti stampare (solo per nexus)",
            "tipo" => "select",
            "valori" => array(
                "default" => "-1",
                "opzioni" => array(
                    array(
                        "label" => "Tutti",
                        "valore" => "-1"
                    ),
                    array("label" => "1","valore" => "1"),
                    array("label" => "2","valore" => "2"),
                    array("label" => "3","valore" => "3"),
                    array("label" => "4","valore" => "4"),
                    array("label" => "5","valore" => "5"),
                    array("label" => "6","valore" => "6"),
                    array("label" => "7","valore" => "7"),
                    array("label" => "8","valore" => "8"),
                    array("label" => "9","valore" => "9"),
                    array("label" => "10","valore" => "10"),
                    array("label" => "11","valore" => "11"),
                    array("label" => "12","valore" => "12"),
                    array("label" => "13","valore" => "13"),
                    array("label" => "14","valore" => "14"),
                    array("label" => "15","valore" => "15"),
                    array("label" => "16","valore" => "16"),
                    array("label" => "17","valore" => "17"),
                    array("label" => "18","valore" => "18"),
                    array("label" => "19","valore" => "19"),
                    array("label" => "20","valore" => "20"),
                    array("label" => "21","valore" => "21"),
                    array("label" => "22","valore" => "22"),
                    array("label" => "23","valore" => "23"),
                    array("label" => "24","valore" => "24"),
                    array("label" => "25","valore" => "25"),
                    array("label" => "26","valore" => "26"),
                    array("label" => "27","valore" => "27"),
                    array("label" => "28","valore" => "28"),
                    array("label" => "29","valore" => "29"),
                    array("label" => "30","valore" => "30")
                )
            ),
            "limiti" => "superutente",
            "selected" => true
        ),
        array(
            "nome" => "modalita_stampa_voti",
            "label" => "Modalità dei voti da stampare",
            "tipo" => "select",
            "valori" => array(
                "default" => "TUTTI",
                "opzioni" => array(
                    array(
                        "label" => "Standard",
                        "valore" => "standard"
                    ),
                    array(
                        "label" => "Solo in lettere",
                        "valore" => "solo_lettere"
                    )
                )
            )
        ),
        array(
            "nome" => "titolo_tabella_voti_schema_primo_quadrimestre",
            "label" => "Personalizza dicitura della prima frazione temporale",
            "tipo" => "text"
        ),
        array(
            "nome" => "includi_materia_religione_schema_primo_quadrimestre",
            "label" => "Includere la materia di tipo religione/alternativa",
            "tipo" => "select",
            "valori" => array(
                "default" => "SI",
                "opzioni" => array(
                    array(
                        "label" => "Si",
                        "valore" => "SI"
                    ),
                    array(
                        "label" => "No",
                        "valore" => "NO"
                    )
                )
            )
        )
//                array(
//                    "name" => "",
//                    "label" => "",
//                    "tipo" => "",
//                    "valori" => array (
//                        "default" => "",
//                        "opzioni" => array(
//                            array(
//                                "label" => "",
//                                "valore" => ""
//                            ),
//                            array(
//                                "label" => "",
//                                "valore" => ""
//                            ),
//                        )
//                    )
//                )
    );
}

function estrai_modelli_word_dati($id_template=null, $locazione=null, $id_classe=null, $periodo=null, $tipo_abbinamento=null) {
    $elenco = [];
    $filtro_id_template = !$id_template ? "" : "AND id_template = '{$id_template}'";
    $filtro_locazione = !$locazione ? "" : "AND locazione = '{$locazione}' ";
    $sql = "SELECT *
            FROM template_word
            WHERE flag_canc = 0
            $filtro_id_template
            $filtro_locazione
            ORDER BY ordinamento
    ";
    $result = pgsql_query($sql) or die("Invalid $sql");

    foreach (pg_fetch_all($result) as $template)
    {
        $template['parametri_personalizzati'] = json_decode($template['parametri_personalizzati'], true);
        $template['abbinamenti'] = estrai_abbinamenti_modello_word($template['id_template'], $id_classe, $periodo, $tipo_abbinamento);

        foreach ($template['abbinamenti'] as $abbinamento) {
            $template['id_classi_selected'][$abbinamento['id_classe']] = $abbinamento['id_classe'];
            $template['periodi_selected'][$abbinamento['periodo']] = $abbinamento['periodo'];
            $template['tipo_abbinamenti_selected'][$abbinamento['tipo_abbinamento']] = $abbinamento['tipo_abbinamento'];
        }

        $elenco[] = $template;
    }

    return $elenco;
}
function inserisci_abbinamento_classe_template_word($id_template, $id_classe, $periodo, $tipo_abbinamento, $current_user)
{
    $sql = "INSERT INTO abbinamenti_template_word_pagelle_classi
            (id_template, id_classe, periodo, tipo_abbinamento)
            VALUES
            ({$id_template}, {$id_classe}, '{$periodo}', '{$tipo_abbinamento}')
    ";
    $result = pgsql_query($sql) or die("Invalid $sql");

    if ($result) {
        $cosa = "$current_user HA INSERITO ABBINAMENTO PAGELLA DOCX CLASSE-PERIODO-TIPO-TEMPLATE: "
                . "id_template: {$id_template} "
                . "id_classe: {$id_classe} "
                . "periodo: {$periodo} "
                . "tipo_abbinamento: {$tipo_abbinamento}";
        inserisci_log_storico($current_user, "ABBINAMENTO_CLASSE_TEMPLATE_WORD", $cosa);
    }
}

function elimina_abbinamenti_classe_template_word($id_template, $id_classe, $periodo='', $tipo_abbinamento='', $current_user)
{
    $sql_periodo = ( $periodo != '' ? "AND periodo = '{$periodo}' " : '' );
    $sql_abbinamento = ( $tipo_abbinamento != '' ? "AND tipo_abbinamento = '{$tipo_abbinamento}' " : '' );

    $sql = "DELETE FROM abbinamenti_template_word_pagelle_classi
            WHERE id_template = {$id_template}
            AND id_classe = {$id_classe}
            $sql_periodo
            $sql_abbinamento;
    ";
    $result = pgsql_query($sql) or die("Invalid $sql");

    if ($result) {
        $cosa = "$current_user HA ELIMINATO GLI ABBINAMENTI PAGELLA DOCX CLASSE-PERIODO-TIPO-TEMPLATE: "
                . "id_template: {$id_template} "
                . "id_classe: {$id_classe} "
                . "periodo: {$periodo} "
                . "tipo_abbinamento: {$tipo_abbinamento}";
        inserisci_log_storico($current_user, "ABBINAMENTO_CLASSE_TEMPLATE_WORD", $cosa);
    }

}
